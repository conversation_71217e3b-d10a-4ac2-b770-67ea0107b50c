<template>
  <a-modal
    v-model:visible="modalVisible"
    title="选择代理机构"
    :width="1000"
    :footer="null"
    :mask-closable="false"
    wrap-class-name="agency-selector-modal"
  >
    <div class="p-4">
      <!-- 搜索区域 -->
      <div class="flex items-center justify-between gap-8 mb-6">
        <div class="flex gap-6">
          <div class="flex items-center">
            <span class="mr-2 whitespace-nowrap">代理机构名称：</span>
            <a-input
              v-model:value="searchForm.agencyName"
              placeholder="请输入代理机构名称"
              allow-clear
            />
          </div>
          <div class="flex items-center">
            <span class="mr-2 whitespace-nowrap">统一社会信用代码：</span>
            <a-input
              v-model:value="searchForm.socialCreditCode"
              placeholder="请输入统一社会信用代码"
              allow-clear
            />
          </div>
        </div>
        <a-space :size="12">
          <a-button
            type="primary"
            @click="setFilterOption(searchForm)"
          >查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-space>
      </div>

      <!-- 表格区域 -->
      <div class="bg-white rounded">
        <a-table
          :dataSource="list"
          :columns="columns"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange"
          :scroll="{ y: 320 }"
          row-key="id"
          :custom-row="handleCustomRow"
          :row-selection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >
          <template #bodyCell="{ column, record }">

          </template>
        </a-table>
      </div>

      <!-- 底部按钮区域 -->
      <div class="flex justify-center mt-6">
        <div class="flex gap-4">
          <a-button
            class="w-32"
            @click="modalVisible = false"
          >关闭</a-button>
          <a-button
            class="w-32"
            type="primary"
            @click="handleConfirm"
          >确定</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { frontTip } from '@/common/js/pub-methods.js';
import { useTable } from '@/composables/useTableHook.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'select', 'cancel']);

// 弹窗可见性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 搜索表单数据
const searchForm = reactive({
  agencyName: '',
  socialCreditCode: ''
});

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    customRender: ({ index }) => index + 1
  },
  {
    title: '代理机构名称',
    dataIndex: 'agencyName',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'socialCreditCode',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    width: 100,
    align: 'center'
  },
  {
    title: '联系人手机',
    dataIndex: 'contactPhone',
    width: 130,
    align: 'center'
  },
  {
    title: '对外联系电话',
    dataIndex: 'externalPhone',
    width: 130,
    align: 'center'
  },
];

// 选中的机构
const selectedAgency = ref(null);
const selectedRowKeys = ref([]);

// 模拟获取代理机构列表数据
const fetchAgencyList = async (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          agencyName: 'xxxxx代理机构',
          socialCreditCode: '91100000101619881W',
          contactPerson: 'XXX',
          contactPhone: '13311112222',
          externalPhone: '13311112222'
        },
        {
          id: '2',
          agencyName: 'xxxxx代理机构',
          socialCreditCode: '91100000101619881W',
          contactPerson: 'XXX',
          contactPhone: '13311112222',
          externalPhone: '13311112222'
        },
        {
          id: '3',
          agencyName: 'xxxxx代理机构',
          socialCreditCode: '91100000101619881W',
          contactPerson: 'XXX',
          contactPhone: '13311112222',
          externalPhone: '13311112222'
        },
        {
          id: '4',
          agencyName: 'xxxxx代理机构',
          socialCreditCode: '91100000101619881W',
          contactPerson: 'XXX',
          contactPhone: '13311112222',
          externalPhone: '13311112222'
        },
        {
          id: '5',
          agencyName: 'xxxxx代理机构',
          socialCreditCode: '91100000101619881W',
          contactPerson: 'XXX',
          contactPhone: '13311112222',
          externalPhone: '13311112222'
        },
        {
          id: '6',
          agencyName: 'xxxxx代理机构',
          socialCreditCode: '91100000101619881W',
          contactPerson: 'XXX',
          contactPhone: '13311112222',
          externalPhone: '13311112222'
        }
      ];

      // 筛选数据
      let filteredData = [...mockData];
      if (params.agencyName) {
        filteredData = filteredData.filter(item =>
          item.agencyName.includes(params.agencyName)
        );
      }
      if (params.socialCreditCode) {
        filteredData = filteredData.filter(item =>
          item.socialCreditCode.includes(params.socialCreditCode)
        );
      }

      resolve({
        rows: filteredData,
        total: 101
      });
    }, 500);
  });
};

// 使用useTable hook管理表格数据
const {
  loading,
  list,
  pagination,
  setFilterOption,
  reset,
  handleTableChange,
  refresh
} = useTable(fetchAgencyList, {
  autoLoad: false,
  initialFilter: searchForm,
  initialPage: 1,
  initialPageSize: 5,
  onSuccess: (res, rows) => {
    // 清空选中状态
    selectedAgency.value = null;
    selectedRowKeys.value = [];
  }
});

// 监听弹窗打开
watch(() => modalVisible.value, (val) => {
  if (val) {
    // 初始化数据
    handleReset();
    refresh();
  }
});

// 处理重置
const handleReset = () => {
  searchForm.agencyName = '';
  searchForm.socialCreditCode = '';
  selectedAgency.value = null;
  selectedRowKeys.value = [];
  reset();
};

// 选择代理机构
const handleSelect = (record) => {
  selectedAgency.value = record;
  selectedRowKeys.value = [record.id];
};


// 处理行选择变化
const onSelectChange = (selectedKeys, selectedRows) => {
  selectedRowKeys.value = selectedKeys;
  if (selectedRows && selectedRows.length > 0) {
    selectedAgency.value = selectedRows[0];
  } else {
    selectedAgency.value = null;
  }
};
// 单选行选择
const handleCustomRow = (row) => {
  return {
    onClick: () => {
      const key = row.id;
      // 单选逻辑：点击行时选中当前行
      onSelectChange([key], [row]);
    },
  };
}

// 确认选择
const handleConfirm = () => {
  if (!selectedAgency.value) {
    frontTip('warning', '请选择一个代理机构');
    return;
  }
  emit('select', selectedAgency.value);
  modalVisible.value = false;
};

</script>

<style lang="scss">
.agency-selector-modal {
  @include custom-modal;


  .ant-table-row-selected>td {
    background-color: #e6f7ff !important;
  }

  .ant-pagination-total-text {
    margin-right: 8px;
  }
}
</style>