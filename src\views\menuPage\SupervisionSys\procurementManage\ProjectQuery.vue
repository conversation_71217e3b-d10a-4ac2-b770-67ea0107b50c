<template>
  <div class="flex flex-col h-full gap-1.5">
    <div class="bg-white rounded-md shadow-sm ">
      <div class="px-4 pt-3 pb-1">
        <a-form
          layout="horizontal"
          class="pt-1 pl-4"
          :model="searchForm"
          ref="searchFormRef"
        >
          <a-row :gutter="24">
            <a-col :span="7">
              <a-form-item
                label="采购项目名称"
                name="projectName"
              >
                <a-input
                  v-model:value="searchForm.projectName"
                  placeholder="请输入"
                  allow-clear
                />
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item
                label="实施方式"
                name="purchaseMethod"
              >
                <a-select
                  v-model:value="searchForm.purchaseMethod"
                  placeholder="请选择"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in purchaseMethodOptions"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="5">
              <a-form-item
                label="状态"
                name="status"
              >
                <a-select
                  v-model:value="searchForm.status"
                  placeholder="请选择"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :span="6"
              class="flex justify-end"
            >
              <a-button
                type="link"
                @click="toggleAdvancedSearch"
                class="text-blue-500 hover:text-blue-400"
              >
                {{ isAdvancedSearch ? '收起' : '展开' }}
                <up-outlined v-if="isAdvancedSearch" />
                <down-outlined v-else />
              </a-button>
              <a-button
                type="primary"
                @click="setFilterOption(searchForm)"
                class="mr-2 rounded"
              >
                <search-outlined />
                查询
              </a-button>
              <a-button
                @click="handleReset"
                class="rounded"
              >
                <reload-outlined />
                重置
              </a-button>
            </a-col>
          </a-row>
          <!-- 可展开收起的额外搜索条件 -->
          <div
            v-show="isAdvancedSearch"
            :class="['animate__animated', isAdvancedSearch ? 'animate__fadeIn' : 'animate__fadeOut']"
          >
            <a-row :gutter="24">
              <a-col :span="7">
                <a-form-item
                  label="采购项目编号"
                  name="projectCode"
                >
                  <a-input
                    v-model:value="searchForm.projectCode"
                    placeholder="请输入"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item
                  label="采购目录"
                  name="purchaseCatalog"
                >
                  <a-select
                    v-model:value="searchForm.purchaseCatalog"
                    placeholder="请选择"
                    allow-clear
                  >
                    <a-select-option
                      v-for="item in purchaseCatalogOptions"
                      :key="item.value"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item
                  label="年度"
                  name="year"
                >
                  <a-date-picker
                    v-model:value="searchForm.year"
                    picker="year"
                    placeholder="请选择"
                    style="width: 100%"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item
                  label="组织形式"
                  name="Organization"
                >
                  <a-select
                    v-model:value="searchForm.Organization"
                    placeholder="请选择"
                    allow-clear
                  >
                    <a-select-option
                      v-for="item in organizationOptions"
                      :key="item.value"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

            </a-row>
            <a-row :gutter="24">
              <a-col :span="7">
                <a-form-item
                  label="受委托代理方"
                  name="agentUnit"
                >
                  <a-input
                    v-model:value="searchForm.agentUnit"
                    placeholder="请输入"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </div>
    </div>

    <div class="flex flex-col flex-1 min-h-0 px-4 py-2 overflow-hidden bg-white rounded-md shadow-sm">
      <base-cardtitle
        title="采购项目列表"
        size="small"
        :dividerVisible="false"
      >
        <template #right-extra>
          <a-button
            type="primary"
            @click="showProjectTypeModal"
            class="rounded"
          >
            <plus-outlined />新增采购项目
          </a-button>
        </template>
      </base-cardtitle>
      <div
        class="flex-1 min-h-0 pt-2 overflow-hidden table-container"
        ref="tableWrapperRef"
      >
        <a-table
          :dataSource="list"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          size="small"
          :scroll="{ x: '100%', y: tableContentMaxHeight }"
          @change="handleTableChange"
          rowKey="id"
          class="h-full table-fixed"
          :sticky="{ offsetHeader: 0 }"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'index'">
              {{ (pagination.current - 1) * pagination.pageSize + record._index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'purchaseMethod'">
              {{ getPurchaseMethodText(text) }}
            </template>
            <!-- 状态列 -->
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(text)">
                {{ getStatusText(text) }}
              </a-tag>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <div class="flex items-center justify-center">
                <a-button
                  type="link"
                  size="small"
                  @click="handleDetail(record)"
                >详情</a-button>

                <a-dropdown>
                  <a-button
                    type="link"
                    size="small"
                  >
                    更多
                    <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="handleEdit(record)">
                        <a-button
                          type="link"
                          size="small"
                        >编辑</a-button>
                      </a-menu-item>
                      <a-menu-item @click="handleChange(record)">
                        <a-button
                          type="link"
                          size="small"
                        >变更</a-button>
                      </a-menu-item>
                      <a-menu-item @click="handleCancel(record)">
                        <a-button
                          type="link"
                          size="small"
                        >作废</a-button>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 项目执行方式选择弹窗组件 -->
    <project-type-selector
      v-model:visible="projectTypeModalVisible"
      @confirm="handleAddProject"
    />

    <!-- 作废/变更原因弹窗组件 -->
    <reason-modal
      v-model:visible="reasonModal.visible"
      :type="reasonModal.type"
      :project-name="reasonModal.record?.projectName || ''"
      :project-id="reasonModal.record?.id || ''"
      @confirm="handleReasonConfirm"
    />


  </div>
</template>

<script setup>
import { frontTip } from "@/common/js/pub-methods.js"
import { formatMoney } from "@/utils/tools.js"
import {
  SearchOutlined,
  ReloadOutlined,
  UpOutlined,
  DownOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';
import { useTable, useTableHeight } from "@/composables/useTableHook.js";
import ProjectTypeSelector from './components/ProjectTypeSelector.vue';
import ReasonModal from './components/ReasonModal.vue';
import {
  PURCHASE_METHOD_OPTIONS,
  PROJECT_STATUS_OPTIONS,
  PURCHASE_CATALOG_OPTIONS,
  ORGANIZATION_OPTIONS
} from '@/common/js/global.js';
const router = useRouter();
const route = useRoute();
// 高级搜索展开/收起状态
const isAdvancedSearch = ref(false);
const searchFormRef = ref(null);
// 搜索表单数据
const searchForm = reactive({
  projectName: '',
  purchaseMethod: undefined,
  status: undefined,
  projectCode: '',
  purchaseCatalog: undefined,
  Organization: undefined,
  year: null,
  agentUnit: ''
});
// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    fixed: 'left'
  },
  {
    title: '采购项目名称',
    dataIndex: 'projectName',
    width: 280,
    ellipsis: true,
    fixed: 'left',
    align: 'center'
  },
  {
    title: '采购项目编号',
    dataIndex: 'projectCode',
    width: 150,
    align: 'center',
    ellipsis: true
  },

  {
    title: '实施方式',
    dataIndex: 'purchaseMethod',
    width: 110,
    align: 'center',
    ellipsis: true
  },
  {
    title: '年度',
    dataIndex: 'year',
    width: 80,
    align: 'center'
  },
  {
    title: '组织形式',
    dataIndex: 'Organization',
    width: 100,
    align: 'center',
    ellipsis: true
  },
  {
    title: '采购目录',
    dataIndex: 'purchaseCatalog',
    width: 100,
    align: 'center',
    ellipsis: true
  },
  {
    title: '采购金额 (元)',
    dataIndex: 'budget',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      // 千分制格式化，保留两位小数
      return formatMoney(text) || '-';
    }
  },
  {
    title: '计划采购日期',
    dataIndex: 'planDate',
    width: 120,
    align: 'center'
  },
  {
    title: '受托代理方',
    dataIndex: 'agentUnit',
    width: 130,
    align: 'center',
    ellipsis: true
  },
  {
    title: '项目创建时间',
    dataIndex: 'prjCreateDate',
    width: 120,
    align: 'center',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    align: 'center',
    fixed: 'right',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    fixed: 'right',
    align: 'center'
  }
];

// 下拉选项常量
const purchaseMethodOptions = ref(PURCHASE_METHOD_OPTIONS);
const statusOptions = ref(PROJECT_STATUS_OPTIONS);
const purchaseCatalogOptions = ref(PURCHASE_CATALOG_OPTIONS);
const organizationOptions = ref(ORGANIZATION_OPTIONS);

// 项目类型选择弹窗相关状态
const projectTypeModalVisible = ref(false);

// 显示项目类型选择弹窗
const showProjectTypeModal = () => {
  projectTypeModalVisible.value = true;
};

// 模拟获取数据（真实接口替换为apis中定义导出的接口名）
const fetchData = async (params) => {
  return new Promise((resolve, reject) => {
    console.log(params, 'xxxx列表参数');
    setTimeout(() => {
      const mockData = Array.from({ length: 50 }).map((_, index) => ({
        id: `${index + 1}`,
        _index: index,
        projectName: `XX市政府采购中心2024年XX设备采购项目${index + 1}`,
        projectCode: index % 2 === 0 ? `${index + 1}` : `B${index + 1}11111122222333333`,
        purchaseMethod: String(Math.floor(Math.random() * 3) + 1),
        year: '2025',
        prjCreateDate: 'yyyy-MM-dd',
        Organization: index % 2 === 0 ? '整包' : '分包',
        purchaseCatalog: index % 3 === 0 ? '货物类' : (index % 3 === 1 ? '工程类' : '服务类'),
        budget: `${10000}.${index < 10 ? '0' + index : index}`,
        planDate: 'yyyy-MM-dd',
        agentUnit: index % 2 === 0 ? '受托代理名称' : '/',
        status: String(Math.floor(Math.random() * 5))
      }));
      console.log(mockData, 'xxx');

      resolve({
        result_code: '0000',
        result_message: '模拟成功',
        rows: mockData,
        total: mockData.length,
      });
    }, 500);
  });

};
// 利用hook组合式获取表格对应数据
const { loading, list, pagination, reset, setFilterOption, handleTableChange, refresh } = useTable(fetchData, {
  autoLoad: true,
  initialFilter: searchForm,
  initialPage: 1,
  initialPageSize: 10,
  onSuccess: (res, rows) => {
    if (res.result_code === '0000') {
      //列表数据成功回调 可以执行后续业务操作，
      console.log('加载列表数据成功！');
    } else {
      frontTip('error', '加载列表数据失败！')
    }
  }
})
// 使用表格高度自适应hook
const { tableWrapperRef, tableHeight: tableContentMaxHeight, calculateTableHeight } = useTableHeight({
  paginationHeight: 48,
  minHeight: 150,
  immediate: true
});

// 切换高级搜索
const toggleAdvancedSearch = () => {
  isAdvancedSearch.value = !isAdvancedSearch.value;
};

// 监听高级搜索状态变化，确保表格高度正确计算
watch(isAdvancedSearch, () => {
  // 延迟计算确保DOM更新完成
  calculateTableHeight();
});


// 处理重置
const handleReset = () => {
  if (searchFormRef.value) searchFormRef.value.resetFields();
  reset();
};

// 处理新增项目
const handleAddProject = (projectType) => {
  if (projectType) {
    // 根据选择的项目类型执行不同的操作
    frontTip('info', `新增采购项目类型: ${projectType}, 功能待实现`);
  }
};

// 处理查看详情
const handleDetail = (record) => {
  // console.log(route.name + 'Details', '详情页路由名');
  handleOperation(record, 'detail')
}

// 通用处理方法：详情、编辑、变更
const handleOperation = (record, operateType) => {
  router.push({
    name: `${route.name}Details`,
    params: {
      id: record.id,
    },
    query: {
      operateType,
      entrance: record.purchaseMethod || '',
    }
  });
};

// 处理编辑
const handleEdit = (record) => {
  handleOperation(record, 'edit');
};

// 处理变更
const handleChange = (record) => {
  handleOperation(record, 'change');
};

// 作废/变更原因弹窗相关状态
const reasonModal = reactive({
  visible: false,
  type: '', // 'cancel' 或 'change'
  record: null
});

// 处理作废
const handleCancel = (record) => {
  reasonModal.type = 'cancel';
  reasonModal.record = record;
  reasonModal.visible = true;
};

// 处理变更原因确认
const handleReasonConfirm = (data) => {
  // 处理提交的原因数据
  if (data.type === 'cancel') {
    // 执行作废操作
    frontTip('success', `已成功作废项目: ${reasonModal.record.projectName}`);
    console.log('作废原因:', data.reason);
    // 这里应该调用实际的作废API
    // await cancelProject(data.id, data.reason);
  } else {
    // 执行变更操作
    frontTip('success', `已成功提交变更申请: ${reasonModal.record.projectName}`);
    console.log('变更原因:', data.reason);
    // 这里应该调用实际的变更API
    // await changeProject(data.id, data.reason);
  }

  // 操作完成后可能需要刷新表格数据
  handleReset()
};

// 获取实施方式文本
const getPurchaseMethodText = (value) => {
  return purchaseMethodOptions.value?.find(item => item.value === value)?.label || '未知';
};

// 获取状态文本
const getStatusText = (value) => {
  return statusOptions.value?.find(item => item.value === value)?.label || '未知';
};

// 获取状态颜色
const getStatusColor = (value) => {
  const colorMap = {
    '0': 'processing',
    '1': 'success',
    '2': 'success',
    '3': 'error'
  };
  return colorMap[value] || 'processing';
};
onActivated(() => {
  refresh();
})
</script>

<style lang="scss" scoped>
:deep(.ant-form-item) {
  margin-bottom: 14px;
}

:deep(.ant-table-wrapper) {
  @include custom-table;
}

:deep(.project-type-modal) {
  .ant-modal-body {
    padding: 16px;
  }

  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
  }

  .ant-modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
  }
}
</style>
