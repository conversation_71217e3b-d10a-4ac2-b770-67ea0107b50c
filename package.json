{"name": "salary-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --m development --port 4010 --open", "dev-d": "vite --m development --port 4010 -d", "dev-f": "vite --m development --port 4010 --force --open", "build-w": "vite build --m production --outDir baseHtml --assetsDir sources -w", "build": "vite build --m production --outDir baseHtml --assetsDir sources", "preview": "vite preview --port 4010", "clean": "rimraf node_modules"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/x6": "^2.18.1", "@antv/x6-vue-shape": "^2.1.2", "ant-design-vue": "3.2.20", "axios": "^1.7.2", "dayjs": "^1.11.12", "gm-crypt": "^0.0.2", "js-base64": "^3.7.7", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^3.0.1", "sm-crypto": "^0.3.13", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.3.3"}, "devDependencies": {"@vitejs/plugin-legacy": "4", "@vitejs/plugin-vue": "^4.5.0", "@vueuse/core": "^13.1.0", "autoprefixer": "^10.4.21", "browserslist": "4.21.5", "postcss": "^8.5.3", "rimraf": "4.3.1", "sass": "^1.77.5", "tailwindcss": "3.4.17", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^0.27.0", "vite": "4.5.10"}, "browserslist": ["last 2 Chrome versions", "last 2 Firefox versions", "last 2 Edge versions"]}