<template>
  <div class="flex flex-col h-full gap-1.5">
    <div class="bg-white rounded-md shadow-sm">
      <div class="px-4 pt-3 pb-1">
        <a-form
          layout="horizontal"
          class="pt-1 pl-4"
          :model="searchForm"
          ref="searchFormRef"
        >
          <a-row :gutter="32">
            <a-col :span="6">
              <a-form-item
                label="采购方案名称"
                name="planName"
              >
                <a-input
                  v-model:value="searchForm.planName"
                  placeholder="请输入"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="采购项目编号"
                name="planNo"
              >
                <a-input
                  v-model:value="searchForm.planNo"
                  placeholder="请输入"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="征集人名称"
                name="collectorName"
              >
                <a-input
                  v-model:value="searchForm.collectorName"
                  placeholder="请输入"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col
              :span="6"
              class="flex justify-end"
            >
              <a-button
                type="primary"
                @click="setFilterOption(searchForm)"
                class="mr-2 rounded"
              >
                <search-outlined />
                查询
              </a-button>
              <a-button
                @click="handleReset"
                class="rounded"
              >
                <reload-outlined />
                重置
              </a-button>
            </a-col>
          </a-row>
          <a-row :gutter="32">
            <a-col :span="6">
              <a-form-item
                label="框架协议类型"
                name="agreementType"
              >
                <a-select
                  v-model:value="searchForm.agreementType"
                  placeholder="请选择"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in agreementTypeOptions"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item
                label="年份"
                name="year"
                :label-col="{ style: { width: '98px', textAlign: 'right' } }"
              >
                <a-date-picker
                  v-model:value="searchForm.year"
                  picker="year"
                  style="width: 100%"
                  placeholder="请选择年份"
                  allow-clear
                  format="YYYY"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="提交时间"
                name="submitTime"
                :label-col="{ style: { width: '84px', textAlign: 'right' } }"
              >
                <a-range-picker
                  v-model:value="searchForm.submitTime"
                  :placeholder="['开始时间', '结束时间']"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </div>

    <div class="flex flex-col flex-1 min-h-0 px-4 py-2 overflow-hidden bg-white rounded-md shadow-sm">
      <base-cardtitle
        title="采购方案查询"
        size="small"
        :dividerVisible="false"
      >
        <template #right-extra>
          <a-radio-group
            v-model:value="activeTabKey"
            button-style="solid"
            @change="handleToDoTabChange"
          >
            <a-radio-button value="todo">待办</a-radio-button>
            <a-radio-button value="done">已办</a-radio-button>
          </a-radio-group>
        </template>
      </base-cardtitle>
      <div
        class="flex-1 min-h-0 pt-2 overflow-hidden table-container"
        ref="tableWrapperRef"
      >
        <a-table
          :dataSource="list"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          size="small"
          :scroll="{ x: '100%', y: tableContentMaxHeight }"
          @change="handleTableChange"
          rowKey="id"
          class="h-full table-fixed"
          :sticky="{ offsetHeader: 0 }"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'index'">
              {{ (pagination.current - 1) * pagination.pageSize + record._index + 1 }}
            </template>
            <!-- 状态列 -->
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(text)">
                {{ getStatusText(text) }}
              </a-tag>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <div class="flex items-center justify-center">
                <a-button
                  type="link"
                  size="small"
                  @click="goHandling(record)"
                >办理</a-button>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { frontTip } from "@/common/js/pub-methods.js"
import {
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons-vue';
import { useTable, useTableHeight } from "@/composables/useTableHook.js";
import emitter from "@/utils/mitt.js";
import { onActivated, onBeforeMount, onMounted } from 'vue';
const route = useRoute();
const router = useRouter();

// 标签页状态
const activeTabKey = ref('todo');
const searchFormRef = ref(null);

// 搜索表单数据
const searchForm = reactive({
  planName: '',
  planNo: '',
  collectorName: '',
  year: undefined,
  agreementType: undefined,
  submitTime: [],
});

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    fixed: 'left'
  },
  {
    title: '采购方案名称',
    dataIndex: 'planName',
    width: 280,
    ellipsis: true,
    fixed: 'left',
    align: 'center'
  },
  {
    title: '采购项目编号',
    dataIndex: 'planNo',
    width: 150,
    align: 'center',
    ellipsis: true
  },
  {
    title: '征集人名称',
    dataIndex: 'collectorName',
    width: 130,
    align: 'center',
    ellipsis: true
  },
  {
    title: '框架协议采购类型',
    dataIndex: 'agreementType',
    width: 150,
    align: 'center'
  },
  {
    title: '框架协议期限(月)',
    dataIndex: 'agreementPeriod',
    width: 150,
    align: 'center'
  },
  {
    title: '年份',
    dataIndex: 'year',
    width: 100,
    align: 'center'
  },
  {
    title: '采购时间',
    dataIndex: 'purchaseTime',
    width: 150,
    align: 'center'
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    width: 150,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    align: 'center',
    fixed: 'right',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 80,
    fixed: 'right',
    align: 'center'
  }
];

// 下拉选项常量
const statusOptions = ref([
  { value: '0', label: '待审核' },
  { value: '1', label: '已审核' },
  { value: '2', label: '已备案' },
  { value: '3', label: '已作废' }
]);



// 框架协议采购类型选项
const agreementTypeOptions = ref([
  { value: '1', label: '开放式' },
  { value: '2', label: '封闭式' }
]);


// 模拟获取数据
const fetchData = async (params) => {
  return new Promise((resolve) => {
    console.log(params, '列表参数');
    setTimeout(() => {
      const mockData = Array.from({ length: 100 }).map((_, index) => ({
        id: `${index + 1}`,
        _index: index,
        planName: `永州市政府采购2024年框架协议项目${index + 1}`,
        planNo: `永采[2023]00004号`,
        collectorName: `永州市政府采购`,
        agreementType: index % 2 === 0 ? '开放式' : '封闭式',
        agreementPeriod: '24',
        year: '2023',
        purchaseTime: '2024-4-21',
        submitTime: '2024-4-21 10:00:21',
        status: index < 5 ? '0' : '1'
      }));

      resolve({
        result_code: '0000',
        result_message: '操作成功',
        rows: mockData,
        total: mockData.length,
      });
    }, 500);
  });
};

// 利用hook组合式获取表格对应数据
const { loading, list, pagination, reset, setFilterOption, handleTableChange, refresh } = useTable(fetchData, {
  autoLoad: true,
  initialFilter: searchForm,
  initialPage: 1,
  initialPageSize: 10,
  onSuccess: (res) => {
    if (res.result_code === '0000') {
      console.log('加载列表数据成功！');
    } else {
      frontTip('error', '加载列表数据失败！')
    }
  }
})
const handleToDoTabChange = (e) => {
  console.log(e, '标签页状态');
  handleReset();
}
// 使用表格高度自适应hook
const { tableWrapperRef, tableHeight: tableContentMaxHeight, calculateTableHeight } = useTableHeight({
  paginationHeight: 48,
  minHeight: 150,
  immediate: true
});

// 监听标签页状态变化，确保表格高度正确计算
watch(activeTabKey, () => {
  // 延迟计算确保DOM更新完成
  calculateTableHeight();
});

// 处理重置
const handleReset = () => {
  if (searchFormRef.value) searchFormRef.value.resetFields();
  reset();
};

// 处理查看详情
const goHandling = (record) => {
  router.push({
    name: `${route.name}Details`,
    params: {
      id: record.id,
    },
    query: {
      operateType: 'audit',
    }
  });
};

// 获取状态文本
const getStatusText = (value) => {
  return statusOptions.value?.find(item => item.value === value)?.label || '未知';
};

// 获取状态颜色
const getStatusColor = (value) => {
  const colorMap = {
    '0': 'warning',
    '1': 'success',
    '2': 'success',
    '3': 'error'
  };
  return colorMap[value] || 'processing';
};

onActivated(() => {
  refresh();
})
</script>

<style lang="scss" scoped>
:deep(.ant-form-item) {
  margin-bottom: 14px;
}

:deep(.ant-table-wrapper) {
  @include custom-table;
}
</style>