<template>
  <div class="user-profile-card">
    <div
      class="flex items-center p-4 transition-all duration-300 border-l-4 border-blue-500 rounded-lg shadow-sm hover:shadow-md bg-gradient-to-r"
    >
      <div class="flex-shrink-0">
        <img
          class="object-cover w-12 h-12 transition-transform duration-300 border-2 border-blue-300 rounded-full hover:scale-105"
          src="@/assets/images/base-icon/icon-avatar.png"
          alt="用户头像"
        />
      </div>
      <div class="ml-4">
        <h1 class="text-lg font-medium animate__animated animate__bounce">
          {{ getPeriod(new Date()) + '好，' + (userName || '访客') }}
        </h1>
        <p class="flex items-center text-sm text-gray-600">
          {{ dateInfo }}
          <span class="ml-2 px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-600">
            {{ loginDays }}
          </span>
        </p>
      </div>
      <div class="ml-auto">
        <div class="text-right">
          <div class="text-sm font-medium text-blue-600">
            {{ systemName }}
          </div>
          <div class="text-xs text-gray-500">{{ systemVersion }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/useUserStore';
import { useSystemStore } from '@/stores/useSystemStore';
import { getPeriod, parseTime } from '@/utils/tools';

const props = defineProps({
  loginDays: {
    type: String,
    default: '第 1 天'
  }
});

const userStore = useUserStore();
const systemStore = useSystemStore();

// 用户名
const userName = computed(() => userStore.getUserName || '');

// 日期信息
const dateInfo = computed(() => parseTime(new Date(), '{y}年{m}月{d}日 {a}'));

// 系统信息
const systemName = computed(() => systemStore.getCurrentSystem?.name || '');
const systemVersion = computed(() => 'v1.0.0');
</script>
<style lang="scss" scoped>
.user-profile-card {
  background-color: $bg-color-welcome-banner;
}
</style>