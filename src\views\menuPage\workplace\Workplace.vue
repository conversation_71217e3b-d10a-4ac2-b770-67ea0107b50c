<template>
  <div
    id="workplace-container-new"
    class="workplace-container"
  >
    <UserProfile :login-days="loginDays" />

    <div class="mt-2">
      <component :is="currentSystemComponent" />
    </div>
    <div
      v-if="errorCom"
      class="h-20 py-6 font-semibold text-center text-red-600 bg-white text-md"
    >
      工作台板块加载失败!请检查....
    </div>
  </div>
</template>

<script setup>
import { useSystemStore } from '@/stores/useSystemStore';
import { useGlobalCacheStore } from '@/stores/useGlobalCacheStore';
import { frontTip } from "@/common/js/pub-methods.js";
// 导入用户信息组件
import UserProfile from './components/UserProfile.vue';
// Store
const systemStore = useSystemStore();

// 模拟数据
const loginDays = ref('第 1 天');
const errorCom = ref(false);

// 当前系统ID
const currentSystemId = computed(() => systemStore.getCurrentSystem?.id || 'main');

// 根据系统ID动态导入工作台组件
const systemComponentMap = {
  'main': () => import('./systems/MainSystem.vue'),
  'sub-sys2': () => import('./systems/WarningSystem.vue'),
  'sub-sys3': () => import('./systems/AnalysisSystem.vue'),
  'sub-sys4': () => import('./systems/SystemManage.vue'),
  'sub-sys5': () => import('./systems/MoreSystem.vue'),
};

const currentSystemComponent = computed(() => {
  const componentLoader = systemComponentMap[currentSystemId.value] || systemComponentMap['main'];
  return defineAsyncComponent({
    loader: async () => {
      const res = await componentLoader();
      errorCom.value = false;
      console.log('工作台组件加载成功');
      return res;
    },
    onError: (error) => {
      frontTip('error', '工作台组件加载动态加载失败', error);
      errorCom.value = true;
      // 可以在这里添加备用组件加载逻辑
    },
  });
});
// 监听系统变化
watch(() => systemStore.getCurrentSystem, (newSystem) => {
  console.log('当前系统已切换:', newSystem?.name);
}, { deep: true });

onMounted(async () => {
  // 初始化数据或调用接口
  console.log('工作台已加载，当前系统:', currentSystemId.value);
  // const globalCacheStore = useGlobalCacheStore();
  // let res = await globalCacheStore.getConfigData();
  // console.log('获取字典数据', res);

});
</script>

<style lang="scss"></style>
