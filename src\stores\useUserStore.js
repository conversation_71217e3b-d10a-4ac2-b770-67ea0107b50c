import { defineStore } from "pinia"
import { sessionCache } from "@/utils/cache.js"
import { useTabsStore } from "./useTabsStore.js"
import { useMenuStore } from "./useMenuStore.js"
import { useSystemStore } from "./useSystemStore.js"
import { useGlobalCacheStore } from "./useGlobalCacheStore.js"
import { frontTip } from "@/common/js/pub-methods.js"
// 去掉API和加密模块引用
// import { login, logout,goCALogin } from '@/api/user.js'
// import { encryptPassword } from '@/utils/crypto.js'

/**
 * 用户状态管理模块
 * 管理用户登录状态、信息及相关操作
 */

/**
 * 用户状态管理Store
 * @returns {Object} 用户状态及相关方法
 */
export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref({});

  // 登录状态
  const isLoggedIn = computed(() => {
    return !!sessionCache.get('tk_sid')
  })

  // 用户名
  const getUserName = computed(() => {
    return userInfo.value?.userName || userInfo.value?.nickname || ''
  })


  // 用户ID
  const getUserId = computed(() => userInfo.value.userId || '')
  //初始化用户信息（刷新有缓存读取）
  const initState = () => {
    // 缓存用户信息
    const cachedUserInfo = sessionCache.getJSON('userInfo');
    // 缓存
    if (cachedUserInfo) {
      userInfo.value = cachedUserInfo
    }
  }

  /**
   * 请求账号密码登录接口
   * @param {Object} loginParams 
   * @returns {Promise<boolean>} 登录结果
   */
  const login = async (loginParams) => {
    try {
      // 模拟登录接口调用
      const { username, password, verifyPicCode } = loginParams
      // 实际项目中替换为真实API调用
      const res = await mockLoginApi(loginParams)
      const { code, message, data, token } = res;
      console.log(res, 'xxxxxxxxx登录结果');
      // 这里模拟登录成功
      if (code === 0 && username === 'admin' && password === 'Hnczt@123') {
        // 保存token
        sessionCache.set('tk_sid', token);
        setUserInfo(data, 'normal')
        return true
      }
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    }
  }
  /**
   * 请求CA登录接口
   * @param {Object} loginParams 
   * @returns {Promise<boolean>} 登录结果
   */
  const calogin = async (loginParams) => {
    const res = await goCALogin(loginParams);
    const { result_code, result_message, data, sid, userLogonEntity } = res;
    console.log(res, 'xxxxxxxxx登录结果');
    // 这里模拟登录成功
    if (result_code == "0000") {
      // 保存token
      sessionCache.set('tk_sid', sid);
      setUserInfo(userLogonEntity, 'ca')
      return true
    }
    return false
  }
  /**
   * 退出登录
   * @returns {Promise<boolean>} 登出结果
   */
  const logout = async () => {
    try {
      // 实际项目中替换为真实API调用
      const res = await mockLogoutApi()
      if (res.code === 0) {
        resetState()
        return true
      }
      return false
    } catch (error) {
      console.error('退出登录失败:', error)
      return false
    }
  }
  const setUserInfo = (info, type) => {
    userInfo.value = info
    userInfo.value.type = type;
    sessionCache.setJSON('userInfo', info)
  }
  /**
   * 重置状态
   */
  const resetState = () => {
    const tabsStore = useTabsStore();
    const menuStore = useMenuStore();
    const systemStore = useSystemStore();
    const globalCacheStore = useGlobalCacheStore();
    // 清除用户信息
    userInfo.value = {}
    // 清除缓存
    sessionCache.clear();
    tabsStore.resetState();
    menuStore.resetState();
    systemStore.resetState();
    globalCacheStore.resetState();
  }

  /**
   * 检查用户是否拥有指定角色
   * @param {string} role 角色名
   * @returns {boolean} 是否拥有该角色
   */
  const hasRole = (role) => {
    const roles = userInfo.value?.roles || []
    return roles.includes(role)
  }

  /**
   * 检查用户是否拥有指定权限
   * @param {string} permission 权限标识
   * @returns {boolean} 是否拥有该权限
   */
  const hasPermission = (permission) => {
    const permissions = userInfo.value?.permissions || []
    return permissions.includes(permission)
  }

  // ====== 以下为模拟API接口 ======

  /**
   * 模拟登录API
   * @param {Object} params - 登录参数
   * @returns {Promise<Object>} 登录结果
   */
  const mockLoginApi = (params) => {
    return new Promise((resolve) => {
      // 模拟网络延迟
      setTimeout(() => {
        resolve({
          code: 0,
          message: '登录成功',
          token: 'mock-token-' + Date.now(),
          data: {
            userId: 1001,
            userName: '管理员',
            avatar: '',
            roles: ['admin'],
          }
        })
      }, 500)
    })
  }



  /**
   * 模拟登出API
   * @returns {Promise<Object>} 登出结果
   */
  const mockLogoutApi = () => {
    return new Promise((resolve) => {
      // 模拟网络延迟
      setTimeout(() => {
        resolve({
          code: 0,
          message: '登出成功'
        })
      }, 500)
    })
  }
  initState();
  return {
    userInfo,
    isLoggedIn,
    getUserName,
    getUserId,
    login,
    calogin,
    logout,
    hasRole,
    hasPermission,

    resetState
  }
})
