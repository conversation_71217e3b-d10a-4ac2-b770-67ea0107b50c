<template>
  <a-sub-menu
    v-if="hasChildren"
    :key="menuItem.menuCode"
  >
    <template #icon>
      <div
        v-if="!menuItem.parentId"
        :class="getIconClass(menuItem)"
      ></div>
    </template>
    <template #title>
      <span :class="[!menuItem.parentId ? 'top-menu-style' : '']">{{ menuItem.menuName }}</span>
    </template>

    <template
      v-for="(child, index) in menuItem.children"
      :key="child.menuCode"
    >
      <sidebar-item
        :menu-item="child"
        @menu-click="onMenuClick"
      />
    </template>
  </a-sub-menu>
  <a-menu-item
    v-else
    :key="menuItem.menuCode"
    @click="onMenuClick(menuItem)"
  >
    <template #icon>
      <div
        v-if="!menuItem.parentId"
        :class="getIconClass(menuItem)"
      ></div>
    </template>
    <!-- <a-badge
      :count="menuItem.count || 0"
      :offset="[12, -1]"
    > -->
    <span v-if="!menuItem.parentId"></span>
    <span :class="[!menuItem.parentId ? 'top-menu-style' : 'child-menu-style ']">{{ menuItem.menuName }}</span>
    <!-- </a-badge> -->
  </a-menu-item>
</template>

<script setup>
import { useTabsStore } from '@/stores/useTabsStore';
const tabsStore = useTabsStore();
const props = defineProps({
  menuItem: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['menu-click']);

// 判断是否有子菜单
const hasChildren = computed(() => {
  return props.menuItem.children && props.menuItem.children.length > 0;
});
// 获取icon
const getIconClass = (menuItem) => {
  const cacheKey = tabsStore.getActiveTab;
  const icon = menuItem.icon || 'icon-base';
  return cacheKey === menuItem.menuCode ? `${icon}_active` : icon;
};
// 判断当前菜单是否是选中菜单

// 点击菜单项
const onMenuClick = (item) => {
  emit('menu-click', item);
};
</script>

<style lang="scss" scoped>
.top-menu-style {
  font-family: $font-title;
}

.child-menu-style {
  margin-left: 2px;
}

/* 注册菜单图标 */
.icon-workplace {
  @include base-icon(18px, 'icon-workplace_menu.svg');
}

.icon-workplace_active {
  @include base-icon(18px, '<EMAIL>');
}

// 基础菜单icon 兜底
.icon-base {
  @include base-icon(16px, 'icon-base_menu.svg');
}

.icon-base_active {
  @include base-icon(16px, '<EMAIL>');
}

// 项目管理
.icon-promanage {
  @include base-icon(18px, 'icon-promanage_menu.svg');
}

//个人办公
.icon-office {
  @include base-icon(18px, 'icon-office_menu.svg');
}

//需求管理
.icon-demand {
  @include base-icon(18px, 'icon-demand_menu.svg');
}

//公告管理
.icon-notice {
  @include base-icon(18px, 'icon-notice_menu.svg');
}
</style>