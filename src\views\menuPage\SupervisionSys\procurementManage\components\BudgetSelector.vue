<template>
  <a-drawer
    v-model:visible="drawerVisible"
    title="预算指标选择"
    :width="500"
    :mask-closable="false"
    :footer-style="{ textAlign: 'right' }"
  >
    <div class="p-4">
      <a-form
        :model="formState"
        layout="vertical"
      >
        <!-- 选择指标输入框 -->
        <a-form-item
          label="选择指标:"
          name="budgetName"
          required
          class="mb-6"
        >
          <div class="flex">
            <a-input
              v-model:value="formState.budgetName"
              placeholder="请选择"
              readonly
              @click="handleOpenBudgetModal"
              class="flex-1 mr-2"
              :class="'cursor-pointer hover:border-blue-400'"
            />
            <a-button
              type="primary"
              @click="handleOpenBudgetModal"
            >选择</a-button>
          </div>
        </a-form-item>

        <!-- 采购目录选择 -->
        <a-form-item
          label="采购目录:"
          name="purchaseCatalog"
          required
          class="mb-6"
        >
          <div class="flex">
            <a-input
              v-model:value="formState.purchaseCatalog"
              placeholder="请选择采购目录"
              readonly
              @click="handleOpenCatalogSelector"
              class="flex-1 mr-2"
              :class="'cursor-pointer hover:border-blue-400'"
            />
            <a-button
              type="primary"
              @click="handleOpenCatalogSelector"
            >选择</a-button>
          </div>
        </a-form-item>

        <!-- 金额信息 -->
        <div class="p-4 mb-6 bg-gray-100 rounded-md">
          <div class="grid grid-cols-1 gap-4">
            <a-form-item
              label="指标总金额:"
              name="totalAmount"
              required
              class="mb-2"
            >
              <a-input-number
                v-model:value="formState.totalAmount"
                style="width: 100%"
                :disabled="true"
                :precision="2"
                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                addon-after="元"
              />
            </a-form-item>

            <a-form-item
              label="可用金额:"
              name="usableAmount"
              required
              class="mb-2"
            >
              <a-input-number
                v-model:value="formState.usableAmount"
                style="width: 100%"
                :disabled="true"
                :precision="2"
                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                addon-after="元"
              />
            </a-form-item>

            <a-form-item
              label="本次使用金额:"
              name="currentAmount"
              required
              class="mb-0"
            >
              <a-input-number
                v-model:value="formState.currentAmount"
                style="width: 100%"
                :precision="2"
                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                addon-after="元"
                :max="formState.usableAmount"
              />
            </a-form-item>
          </div>
        </div>
      </a-form>

      <!-- 底部按钮区域 -->
      <div class="flex justify-end mt-6">
        <div class="flex gap-4">
          <a-button @click="drawerVisible =false">关闭</a-button>
          <a-button
            type="primary"
            @click="handleConfirm"
            :disabled="!formState.budgetName"
          >确定</a-button>
        </div>
      </div>
    </div>
  </a-drawer>

  <!-- 使用单独的组件进行指标选择 -->
  <budget-index-selector
    v-model:visible="budgetModalVisible"
    @select="handleSelectBudget"
  />

  <!-- 采购目录选择器 -->
  <purchase-catalog-selector
    v-model:visible="catalogSelectorVisible"
    v-model:modelValue="formState.purchaseCatalogVal"
    :tree-data="purchaseCatalogOptions"
    @select="handlePurchaseCatalogSelected"
  />
</template>

<script setup>
import { frontTip } from '@/common/js/pub-methods.js';
import PurchaseCatalogSelector from './PurchaseCatalogSelector.vue';
import BudgetIndexSelector from './BudgetIndexSelector.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'select', 'cancel']);

// 抽屉可见性
const drawerVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单数据
const formState = reactive({
  budgetName: '',
  budgetId: '',
  purchaseCatalog: '', //展示值
  purchaseCatalogVal: '', // 选中值
  totalAmount: 1000000.00,
  usableAmount: 1000000.00,
  currentAmount: 1000000.00,
  year: '2024',
  fundNature: '一般公共预算资金',
  indexType: '部门资金'
});

// 指标选择弹窗可见性
const budgetModalVisible = ref(false);

// 采购目录选择弹窗可见性
const catalogSelectorVisible = ref(false);

// 采购目录树形数据
const purchaseCatalogOptions = ref([
  {
    title: 'A 货物',
    value: 'A',
    key: 'A',
    disableCheckbox: true,
    children: [
      {
        title: 'A020000C0 设备',
        value: 'A020000C0',
        key: 'A020000C0',
        disableCheckbox: true,
        children: [
          {
            title: 'A02010000 信息化设备',
            value: 'A02010000',
            key: 'A02010000',
            disableCheckbox: true,
            children: [
              {
                title: 'A02010100 计算机',
                value: 'A02010100',
                key: 'A02010100',
                children: [
                  {
                    title: '巨型计算机',
                    value: 'A02010101',
                    key: 'A02010101',
                    isLeaf: true,
                    purchaseType: '分散采购'
                  },
                  {
                    title: '大型计算机',
                    value: 'A02010102',
                    key: 'A02010102',
                    isLeaf: true,
                    purchaseType: '分散采购'
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
]);

// 监听抽屉打开
watch(() => drawerVisible.value, (val) => {
  if (val) {
    // 初始化表单数据
    formState.budgetName = '';
    formState.budgetId = '';
    formState.purchaseCatalog = '';
    formState.totalAmount = 1000000.00;
    formState.usableAmount = 1000000.00;
    formState.currentAmount = 1000000.00;
    formState.year = '2024';
    formState.fundNature = '一般公共预算资金';
  }
});

// 打开指标选择弹窗
const handleOpenBudgetModal = () => {
  budgetModalVisible.value = true;
};

// 打开采购目录选择弹窗
const handleOpenCatalogSelector = () => {
  catalogSelectorVisible.value = true;
};
// 处理采购目录选择
const handlePurchaseCatalogSelected = (selectedValues) => {
  formState.purchaseCatalog = selectedValues;
  catalogSelectorVisible.value = false;
  frontTip('success', '采购目录选择成功');
};
// 如果采购目录的值监听为空，那么对应展示项也要同步
watch(() => formState.purchaseCatalogVal, (newVal) => {
  if (!newVal) {
    formState.purchaseCatalog = '';
  }
})

// 处理预算指标选择
const handleSelectBudget = (budget) => {
  formState.budgetName = budget.budgetName;
  formState.budgetId = budget.id;
  formState.totalAmount = budget.amount;
  formState.usableAmount = budget.usableAmount;
  formState.currentAmount = budget.usableAmount;
  formState.indexType = budget.indexType;
  formState.year = budget.year;
  formState.fundNature = budget.fundNature;
};


// 确认选择
const handleConfirm = () => {
  if (!formState.budgetName) {
    frontTip('warning', '请选择一个预算指标');
    return;
  }

  if (!formState.purchaseCatalog) {
    frontTip('warning', '请选择采购目录');
    return;
  }

  // 构建返回数据
  const budgetData = {
    id: formState.budgetId,
    budgetName: formState.budgetName,
    indexType: formState.indexType,
    amount: formState.totalAmount,
    usableAmount: formState.usableAmount,
    year: formState.year,
    fundNature: formState.fundNature,
    catalogCode: formState.purchaseCatalog,
    currentAmount: formState.currentAmount
  };

  emit('select', budgetData);
  drawerVisible.value = false;
};
watch(() => props.data, (newVal) => {
  // 如果是编辑直接取newVal中的值进行赋值初始化
}, { immediate: true })

</script>

<style lang="scss"></style>