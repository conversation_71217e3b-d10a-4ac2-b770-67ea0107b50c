<template>
  <div class="flex flex-col h-full gap-1.5">
    <div class="bg-white rounded-md shadow-sm ">
      <div class="px-4 pt-3 pb-1">
        <a-form
          layout="horizontal"
          class="pt-1 pl-4"
          :model="searchForm"
          ref="searchFormRef"
        >
          <a-row :gutter="24">
            <a-col :span="6">
              <a-form-item
                label="采购项目名称"
                name="projectName"
              >
                <a-input
                  v-model:value="searchForm.projectName"
                  placeholder="请输入"
                  allow-clear
                />
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item
                label="公告类型"
                name="noticeType"
              >
                <a-select
                  v-model:value="searchForm.noticeType"
                  placeholder="请选择"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in noticeTypeOptions"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                label="采购人名称"
                name="purchaserName"
              >
                <a-input
                  v-model:value="searchForm.purchaserName"
                  placeholder="请输入"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col
              :span="6"
              class="flex justify-end"
            >
              <a-button
                type="link"
                @click="toggleAdvancedSearch"
                class="text-blue-500 hover:text-blue-400"
              >
                {{ isAdvancedSearch ? '收起' : '展开' }}
                <up-outlined v-if="isAdvancedSearch" />
                <down-outlined v-else />
              </a-button>
              <a-button
                type="primary"
                @click="setFilterOption(searchForm)"
                class="mr-2 rounded"
              >
                <search-outlined />
                查询
              </a-button>
              <a-button
                @click="handleReset"
                class="rounded"
              >
                <reload-outlined />
                重置
              </a-button>
            </a-col>
          </a-row>
          <!-- 可展开收起的额外搜索条件 -->
          <div
            v-show="isAdvancedSearch"
            :class="['animate__animated', isAdvancedSearch ? 'animate__fadeIn' : 'animate__fadeOut']"
          >
            <a-row :gutter="24">
              <a-col :span="6">
                <a-form-item
                  label="采购项目编号"
                  name="projectCode"
                >
                  <a-input
                    v-model:value="searchForm.projectCode"
                    placeholder="请输入"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </div>
    </div>

    <div class="flex flex-col flex-1 min-h-0 px-4 py-2 overflow-hidden bg-white rounded-md shadow-sm">
      <base-cardtitle
        title="采购征集公告列表"
        size="small"
        :dividerVisible="false"
      >
      </base-cardtitle>
      <div
        class="flex-1 min-h-0 pt-2 overflow-hidden table-container"
        ref="tableWrapperRef"
      >
        <a-table
          :dataSource="list"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          size="small"
          :scroll="{ x: '100%', y: tableContentMaxHeight }"
          @change="handleTableChange"
          rowKey="id"
          class="h-full table-fixed"
          :sticky="{ offsetHeader: 0 }"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'index'">
              {{ (pagination.current - 1) * pagination.pageSize + record._index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'noticeType'">
              {{ getNoticeTypeText(text) }}
            </template>
            <template v-if="column.dataIndex === 'action'">
              <div class="flex items-center justify-center">
                <a-button
                  type="link"
                  size="small"
                  @click="handleDetail(record)"
                >详情</a-button>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { frontTip } from "@/common/js/pub-methods.js"
import {
  SearchOutlined,
  ReloadOutlined,
  UpOutlined,
  DownOutlined,
} from '@ant-design/icons-vue';
import { useTable, useTableHeight } from "@/composables/useTableHook.js";

// 路由相关
const router = useRouter();
const route = useRoute();

// 高级搜索展开/收起状态
const isAdvancedSearch = ref(false);
const searchFormRef = ref(null);

// 搜索表单数据
const searchForm = reactive({
  projectName: '',
  noticeType: undefined,
  purchaserName: '',
  projectCode: ''
});

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    fixed: 'left'
  },
  {
    title: '采购项目名称',
    dataIndex: 'projectName',
    width: 280,
    ellipsis: true,
    fixed: 'left',
    align: 'center'
  },
  {
    title: '采购项目编号',
    dataIndex: 'projectCode',
    width: 150,
    align: 'center',
    ellipsis: true
  },
  {
    title: '公告类型',
    dataIndex: 'noticeType',
    width: 150,
    align: 'center',
    ellipsis: true
  },
  {
    title: '采购人名称',
    dataIndex: 'purchaserName',
    width: 150,
    align: 'center'
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    width: 100,
    align: 'center'
  },
  {
    title: '联系方式',
    dataIndex: 'contactWay',
    width: 150,
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 100,
    fixed: 'right',
    align: 'center'
  }
];

// 下拉选项常量
const noticeTypeOptions = ref([
  { value: '1', label: '封闭式框架协议征集公告' },
  { value: '2', label: '开放式框架协议征集公告' }
]);

// 模拟获取数据（真实接口替换为apis中定义导出的接口名）
const fetchData = async (params) => {
  return new Promise((resolve, reject) => {
    console.log(params, '公告列表参数');
    setTimeout(() => {
      const mockData = Array.from({ length: 6 }).map((_, index) => ({
        id: `${index + 1}`,
        _index: index,
        projectName: `XX市各级党政机关2025年XX服务框架协议采购项目`,
        projectCode: `永道财预【2025】054号`,
        noticeType: index % 2 === 0 ? '1' : '2',
        purchaserName: `征集人名称`,
        contactPerson: `征集人名称`,
        contactWay: `*********-培训服务`
      }));

      resolve({
        result_code: '0000',
        result_message: '模拟成功',
        rows: mockData,
        total: mockData.length,
      });
    }, 500);
  });
};

// 利用hook组合式获取表格对应数据
const { loading, list, pagination, reset, setFilterOption, handleTableChange, refresh } = useTable(fetchData, {
  autoLoad: true,
  initialFilter: searchForm,
  initialPage: 1,
  initialPageSize: 10,
  onSuccess: (res, rows) => {
    if (res.result_code === '0000') {
      console.log('加载列表数据成功！');
    } else {
      frontTip('error', '加载列表数据失败！')
    }
  }
});

// 使用表格高度自适应hook
const { tableWrapperRef, tableHeight: tableContentMaxHeight, calculateTableHeight } = useTableHeight({
  paginationHeight: 48,
  minHeight: 150,
  immediate: true
});

// 切换高级搜索
const toggleAdvancedSearch = () => {
  isAdvancedSearch.value = !isAdvancedSearch.value;
};

// 监听高级搜索状态变化，确保表格高度正确计算
watch(isAdvancedSearch, () => {
  // 延迟计算确保DOM更新完成
  calculateTableHeight();
});

// 处理重置
const handleReset = () => {
  if (searchFormRef.value) searchFormRef.value.resetFields();
  reset();
};

// 处理查看详情
const handleDetail = (record) => {
  router.push({
    name: `${route.name}Details`,
    params: {
      id: record.id,
    },
    query: {
      operateType: 'detail',
      prjName: record.projectName || '',
    }
  });
};

// 获取公告类型文本
const getNoticeTypeText = (value) => {
  return noticeTypeOptions.value?.find(item => item.value === value)?.label || '未知';
};

onActivated(() => {
  refresh();
});
</script>

<style lang="scss" scoped>
:deep(.ant-form-item) {
  margin-bottom: 14px;
}

:deep(.ant-table-wrapper) {
  @include custom-table;
}
</style>