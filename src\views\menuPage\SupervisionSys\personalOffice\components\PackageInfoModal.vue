<template>
  <a-modal
    v-model:visible="modalVisible"
    title="包信息"
    :width="1200"
    :footer="null"
    :mask-closable="false"
    style="top:60px"
    :destroy-on-close="true"
    wrap-class-name="package-info-modal"
  >
    <div class="px-4">
      <!-- Tab切换部分 -->
      <div class="mb-6">
        <a-tabs
          v-model:activeKey="activeTab"
          class="package-tabs"
        >
          <a-tab-pane
            key="basic"
            tab="基本信息"
          >
            <base-cardtitle
              title="包信息"
              size="small"
              class="mt-2 mb-4"
              :divider-visible="false"
            />

            <a-descriptions
              :column="2"
              bordered
              size="middle"
            >
              <a-descriptions-item label="包号">{{ packageData.packageNo }}</a-descriptions-item>
              <a-descriptions-item label="包名称">{{ packageData.packageName }}</a-descriptions-item>
              <a-descriptions-item label="采购类型">{{ packageData.purchaseType }}</a-descriptions-item>
              <a-descriptions-item label="采购方式">{{ packageData.purchaseMethod }}</a-descriptions-item>
              <a-descriptions-item label="评审规则">{{ packageData.evaluationRule }}</a-descriptions-item>
              <a-descriptions-item label="是否涉及进口产品采购">{{ packageData.isImport ? '是' : '否' }}</a-descriptions-item>
              <a-descriptions-item label="是否允许联合体投标">{{ packageData.isJointBidding ? '是' : '否'
              }}</a-descriptions-item>
              <a-descriptions-item label="向中小企业预留采购份额">{{ packageData.isReservedForSME ? '是' : '否'
              }}</a-descriptions-item>
              <a-descriptions-item label="是否允许合同分包">{{ packageData.isContractSubcontract ? '是' : '否'
              }}</a-descriptions-item>
              <a-descriptions-item label="预留份额措施名称">{{ packageData.reservedMeasureName }}</a-descriptions-item>
              <a-descriptions-item label="是否予以价格评审优惠">{{ packageData.isPricePreference ? '是' : '否'
              }}</a-descriptions-item>
              <a-descriptions-item label="一阶段入围淘汰率">{{ packageData.firstStageEliminationRate }}%</a-descriptions-item>
              <a-descriptions-item label="价格评审优惠比例">{{ packageData.pricePreferenceRate }}%</a-descriptions-item>
              <a-descriptions-item
                label="二阶段成交方法"
                :span="2"
              >{{ packageData.secondStageMethod }}</a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          <a-tab-pane
            key="target"
            tab="标的信息"
          >
            <base-cardtitle
              title="标的信息"
              class="mt-2 mb-4"
              size="small"
              :divider-visible="false"
            />
            <a-table
              :dataSource="targetData"
              :columns="targetColumns"
              rowKey="id"
              :pagination="false"
              :scroll="{ x: '100%', y: 380 }"
              bordered
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'price' || column.dataIndex === 'totalPrice'">
                  {{ formatMoney(record[column.dataIndex]) }}
                </template>
                <template
                  v-if="column.dataIndex === 'isImport' || column.dataIndex === 'isEnergySaving' || column.dataIndex === 'isEnvironmentProtection'"
                >
                  {{ record[column.dataIndex] ? '是' : '否' }}
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          <a-tab-pane
            key="supplier"
            tab="供应商资格信息"
          >
            <base-cardtitle
              title="供应商资格信息"
              size="small"
              class="mt-2 mb-4"
              :divider-visible="false"
            />
            <a-table
              :dataSource="supplierQualificationData"
              :columns="supplierColumns"
              rowKey="id"
              :pagination="false"
              :scroll="{ y: 500 }"
              bordered
            >
              <template #bodyCell="{ column, record, text }">
                <template v-if="column.dataIndex === 'type'">
                  <a-tag color="processing">
                    {{ text }}
                  </a-tag>
                </template>
                <template v-if="column.dataIndex === 'requires'">
                  <div class="break-words whitespace-pre-wrap">{{ record.requires }}</div>
                </template>
                <template v-if="column.dataIndex === 'description'">
                  <div class="break-words whitespace-pre-wrap">{{ record.description }}</div>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 底部按钮 -->
      <div class="flex justify-center mt-4">
        <a-button
          class="w-32"
          @click="closeModal"
        >关闭</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { formatMoney } from "@/utils/tools.js";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  packageId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['update:visible']);

// 弹窗可见性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 当前激活的标签页
const activeTab = ref('basic');

// 包基本信息数据
const packageData = reactive({
  packageNo: '包1',
  packageName: '计算机器采购包',
  purchaseType: '货物',
  purchaseMethod: '框架协议采购',
  evaluationRule: '价格优先',
  isImport: true,
  isJointBidding: true,
  isReservedForSME: true,
  isContractSubcontract: true,
  reservedMeasureName: '采购项目整体预留',
  isPricePreference: true,
  firstStageEliminationRate: 20,
  pricePreferenceRate: 20,
  secondStageMethod: '直接选定、二次竞价'
});

// 采购标的列定义
const targetColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    fixed: 'left',
    customRender: ({ index }) => index + 1
  },
  {
    title: '采购标的名称',
    dataIndex: 'name',
    width: 150,
    align: 'center'
  },
  {
    title: '采购标的类型',
    dataIndex: 'type',
    width: 120,
    align: 'center'
  },
  {
    title: '采购目录',
    dataIndex: 'category',
    width: 150,
    align: 'center'
  },
  {
    title: '所属行业',
    dataIndex: 'industry',
    width: 100,
    align: 'center'
  },
  {
    title: '是否采购进口产品',
    dataIndex: 'isImport',
    width: 140,
    align: 'center'
  },
  {
    title: '是否节能环水',
    dataIndex: 'isEnergySaving',
    width: 120,
    align: 'center'
  },
  {
    title: '是否绿色环保',
    dataIndex: 'isEnvironmentProtection',
    width: 120,
    align: 'center'
  },
  {
    title: '计量单位',
    dataIndex: 'unit',
    width: 100,
    align: 'center'
  },
  {
    title: '最高限价',
    dataIndex: 'price',
    width: 120,
    align: 'right'
  },
  {
    title: '报价方式',
    dataIndex: 'quoteType',
    width: 100,
    align: 'center',
    fixed: 'right'
  }
];

// 供应商资格列定义
const supplierColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    customRender: ({ index }) => index + 1
  },
  {
    title: '资格条件类型',
    dataIndex: 'type',
    width: 150,
    align: 'center'
  },
  {
    title: '审查要求',
    dataIndex: 'requires',
    width: 300,
    align: 'center'
  },
  {
    title: '要求说明',
    dataIndex: 'description',
    width: 300,
    align: 'center'
  }
];

// 采购标的数据
const targetData = [
  {
    id: '1',
    name: '台式计算机',
    type: '主商品',
    category: 'XXXXX',
    industry: 'XXXXX',
    isImport: false,
    isEnergySaving: false,
    isEnvironmentProtection: false,
    unit: '台',
    price: 7000,
    quoteType: '单价'
  },
  {
    id: '2',
    name: '台式计算机',
    type: '配件',
    category: 'XXXXX',
    industry: 'XXXXX',
    isImport: false,
    isEnergySaving: false,
    isEnvironmentProtection: false,
    unit: '台',
    price: 7000,
    quoteType: '总价'
  },
  {
    id: '3',
    name: '台式计算机',
    type: '主商品',
    category: 'XXXXX',
    industry: 'XXXXX',
    isImport: false,
    isEnergySaving: false,
    isEnvironmentProtection: false,
    unit: '台',
    price: 7000,
    quoteType: '单价'
  },
  {
    id: '4',
    name: '台式计算机',
    type: '配件',
    category: 'XXXXX',
    industry: 'XXXXX',
    isImport: false,
    isEnergySaving: false,
    isEnvironmentProtection: false,
    unit: '台',
    price: 7000,
    quoteType: '总价'
  },
  {
    id: '5',
    name: '台式计算机',
    type: '主商品',
    category: 'XXXXX',
    industry: 'XXXXX',
    isImport: false,
    isEnergySaving: false,
    isEnvironmentProtection: false,
    unit: '台',
    price: 7000,
    quoteType: '单价'
  }
];

// 供应商资格数据
const supplierQualificationData = [
  {
    id: '1',
    type: '基本资质',
    requires: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    description: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'
  },
  {
    id: '2',
    type: '特殊资质',
    requires: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    description: 'XXXXXXXXXXXXXXXXXXXXXXXX'
  }
];

// 监听packageId变化，加载包信息
watch(() => props.packageId, (newId) => {
  if (newId && modalVisible.value) {
    // 这里可以添加根据ID加载包信息的逻辑
    console.log('加载包ID:', newId);
    // 重置为第一个标签
    activeTab.value = 'basic';
  }
});

// 关闭弹窗
const closeModal = () => {
  modalVisible.value = false;
};
</script>

<style lang="scss">
.package-info-modal {
  @include custom-modal;

  :deep(.ant-tabs-tab) {
    padding: 12px 20px;
    margin: 0;

    &.ant-tabs-tab-active {
      font-weight: bold;
      background-color: #1890ff;
      border-radius: 4px 4px 0 0;

      .ant-tabs-tab-btn {
        color: white;
      }
    }
  }

  :deep(.ant-tabs-nav::before) {
    border-bottom: none;
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: #f0f7ff;
    font-weight: bold;
  }
}
</style>