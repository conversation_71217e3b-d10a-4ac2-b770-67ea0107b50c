<template>
  <div class="project-details-page">
    <div
      class="flex flex-col h-full"
      ref="pageRef"
    >
      <a-page-header
        :ghost="false"
        sub-title="(框架协议一阶段)"
        @back="handleBack"
      >
        <template #title>
          <span class="page-header-title">采购项目立项详情 </span>
        </template>
        <template #tags>
          <a-tag
            v-if="pageType"
            :color="pageTypeTagStyle"
          >{{ typeMap[pageType] || '' }}</a-tag>
        </template>
        <template #extra>
          <a-space>
            <a-button
              class="min-w-[128px]"
              @click="handleViewProcess"
              type="default"
            >
              <template #icon><eye-outlined /></template>
              流程查看
            </a-button>
            <a-button
              v-if="!isDetailMode"
              @click="handleSave"
              type="primary"
              class="bg-green-500 border-green-500 hover:bg-green-600 hover:border-green-600"
            >
              <template #icon><save-outlined /></template>
              保存
            </a-button>
            <a-button
              v-if="!isDetailMode"
              @click="handleSubmit"
              type="primary"
            >
              <template #icon><check-outlined /></template>
              提交
            </a-button>
          </a-space>
        </template>
      </a-page-header>
      <div
        class="white-space"
        style="height: 6px;"
      ></div>
      <div class="px-6 bg-white">
        <a-tabs v-model:activeKey="activeTabKey">
          <a-tab-pane
            key="basicInfo"
            tab="项目基本信息"
          ></a-tab-pane>
          <a-tab-pane
            key="policyInfo"
            tab="政策执行信息"
          ></a-tab-pane>
          <a-tab-pane
            key="attachmentInfo"
            tab="附件信息"
          ></a-tab-pane>
        </a-tabs>
      </div>
      <!-- <div class="white-space"></div> -->
      <div
        class="white-space"
        style="height: 6px;"
      ></div>
      <div class="flex-1 overflow-x-hidden overflow-y-auto pyy-4">
        <div
          v-show="activeTabKey === 'basicInfo'"
          class="animate__animated animate__fadeIn animate__faster"
        >
          <a-card :bordered="false">
            <template #title>
              <base-cardtitle
                title="基础信息"
                size="small"
                :divider-visible="false"
              />
            </template>
            <a-form
              ref="basicFormRef"
              class="pt-2"
              :model="formState"
              :rules="basicFormRules"
              layout="horizontal"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }"
            >
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item
                    label="采购项目名称"
                    name="projectName"
                    required
                  >
                    <a-input
                      v-model:value="formState.projectName"
                      :placeholder="isDetailMode ? '' : '请输入'"
                      allow-clear
                      :disabled="isDetailMode"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="采购项目编号"
                    name="projectCode"
                    required
                  >
                    <a-input
                      v-model:value="formState.projectCode"
                      :placeholder="isDetailMode ? '' : '提交后从中台获取项目编号'"
                      disabled
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item
                    label="年度"
                    name="year"
                    required
                  >
                    <a-select
                      v-model:value="formState.year"
                      :placeholder="isDetailMode ? '' : '默认当前年度'"
                      disabled
                      allow-clear
                    >
                      <a-select-option
                        v-for="option in yearOptions"
                        :value="option.value"
                        :key="option.value"
                      >{{ option.label
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="实施方式"
                    name="purchaseMethod"
                    required
                  >
                    <a-select
                      v-model:value="formState.purchaseMethod"
                      disabled
                      :placeholder="isDetailMode ? '' : '当前实施方式'"
                      allow-clear
                    >
                      <a-select-option
                        v-for="option in purchaseMethodOptions"
                        :value="option.value"
                        :key="option.value"
                      >{{
                        option.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item
                    label="采购单位信息"
                    name="purchaseUnitInfo"
                    required
                  >
                    <a-input
                      v-model:value="formState.purchaseUnitInfo"
                      :placeholder="isDetailMode ? '' : '默认操作人采购单位'"
                      disabled
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="财政区划"
                    name="financialDivision"
                    required
                  >
                    <a-input
                      v-model:value="formState.financialDivision"
                      :placeholder="isDetailMode ? '' : '默认采购单位财政区划'"
                      disabled
                    />
                  </a-form-item>
                </a-col>

              </a-row>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item
                    label="组织形式"
                    name="organization"
                    required
                  >
                    <a-select
                      v-model:value="formState.organization"
                      :placeholder="isDetailMode ? '' : '请选择'"
                      allow-clear
                      :disabled="isDetailMode"
                    >
                      <a-select-option
                        v-for="option in organizationOptions"
                        :value="option.value"
                        :key="option.value"
                      >{{
                        option.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="受托代理机构"
                    name="agentUnit"
                  >
                    <a-input
                      v-model:value="formState.agentUnit"
                      :placeholder="isDetailMode ? '' : '请点击选择'"
                      allow-clear
                      readonly
                      :disabled="isDetailMode"
                      :class="!isDetailMode ? 'cursor-pointer hover:border-blue-400' : ''"
                      @click="!isDetailMode && openAgencySelector()"
                    >
                      <template #suffix>
                        <search-outlined class="text-gray-400" />
                      </template>
                    </a-input>
                  </a-form-item>
                </a-col>

              </a-row>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item
                    label="项目类别"
                    name="prjType"
                    required
                  >
                    <a-input
                      v-model:value="formState.prjType"
                      :placeholder="isDetailMode ? '' : '请选择'"
                      style="width: 100%"
                      allow-clear
                      :disabled="isDetailMode"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="计划采购日期"
                    name="planDate"
                    required
                  >
                    <a-date-picker
                      v-model:value="formState.planDate"
                      :placeholder="isDetailMode ? '' : '请选择'"
                      style="width: 100%"
                      format="YYYY-MM-DD"
                      :disabled="isDetailMode"
                    />
                  </a-form-item>
                </a-col>

              </a-row>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item
                    label="采购金额"
                    name="budgetMoney"
                    required
                  >
                    <a-input-number
                      v-model:value="formState.budgetMoney"
                      :placeholder="isDetailMode ? '' : '请输入'"
                      :disabled="isDetailMode"
                      style="width: 100%"
                      :precision="2"
                      :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                      :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                      addon-after="元"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="采购目录"
                    name="purchaseCatalogVal"
                    required
                  >
                    <a-select
                      v-model:value="formState.purchaseCatalogVal"
                      :placeholder="isDetailMode ? '' : '请选择采购目录'"
                      allowClear
                      :disabled="isDetailMode"
                      :open="false"
                      :class="!isDetailMode ? 'cursor-pointer hover:border-blue-400' : ''"
                      @click="!isDetailMode && openPurchaseCatalogSelector()"
                    >
                      <template #suffix>
                        <search-outlined class="text-gray-400" />
                      </template>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">

              </a-row>
            </a-form>
          </a-card>
          <div class="white-space"></div>
          <a-card :bordered="false">
            <template #title>
              <base-cardtitle
                title="项目属性信息"
                size="small"
                :divider-visible="false"
              />
            </template>
            <div class="grid grid-cols-1 py-2 pl-16 md:grid-cols-2 lg:grid-cols-3 gap-y-2">
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否项目采购：</span>
                <a-radio-group
                  v-model:value="formState.isProjectPurchase"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否统采分签项目：</span>
                <a-radio-group
                  v-model:value="formState.isCollectAndSignPrj"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否PPP采购项目：</span>
                <a-radio-group
                  v-model:value="formState.isPPPProject"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否一采多年项目：</span>
                <a-radio-group
                  v-model:value="formState.isPurAndMoreYear"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否832平台采购项目：</span>
                <a-radio-group
                  v-model:value="formState.is832PurPrj"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否适用于招标投标法项目：</span>
                <a-radio-group
                  v-model:value="formState.isTenderLawProject"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>

              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否补充合同项目：</span>
                <a-radio-group
                  v-model:value="formState.isSMEProject"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否旧系统历年项目：</span>
                <a-radio-group
                  v-model:value="formState.isSMEProjecisOldSystemProjectt"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否涉密采购项目：</span>
                <a-radio-group
                  v-model:value="formState.isConfidentialProject"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
            </div>
          </a-card>
        </div>
        <div
          v-show="activeTabKey === 'policyInfo'"
          class="animate__animated animate__fadeIn animate__faster"
        >
          <a-card :bordered="false">
            <template #title>
              <base-cardtitle
                title="中小企业政策执行信息"
                size="small"
                :divider-visible="false"
              />
            </template>
            <a-form
              ref="policyFormRef"
              class="pt-2"
              :model="policyFormState"
              layout="horizontal"
              :label-col="{ span: 8 }"
              :wrapper-col="{ span: 14 }"
            >
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item
                    label="是否适宜中小企业"
                    name="isReservedForSME"
                    :rules="[{ required: true, message: '请选择是否适宜中小企业', trigger: 'change' }]"
                  >
                    <a-select
                      v-model:value="policyFormState.isReservedForSME"
                      :placeholder="isDetailMode ? '' : '请选择'"
                      allow-clear
                      :disabled="isDetailMode"
                    >
                      <a-select-option
                        v-for="option in yesNoOptions"
                        :value="option.value"
                        :key="option.value"
                      >{{
                        option.label
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    label="中小企业预留份额措施"
                    name="smeDiscountRate"
                    v-if="policyFormState.isReservedForSME === '1'"
                    :rules="[{ required: true, message: '请选择中小企业预留份额措施', trigger: 'change' }]"
                  >
                    <a-select
                      v-model:value="policyFormState.smeDiscountRate"
                      :placeholder="isDetailMode ? '' : '请选择'"
                      allow-clear
                      :disabled="isDetailMode"
                    >
                      <a-select-option
                        v-for="option in smeDiscountRateOptions"
                        :value="option.value"
                        :key="option.value"
                      >{{
                        option.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="24">
                  <a-form-item
                    label="不适宜原因"
                    name="unsuitableReason"
                    :labelCol="{ span: 4 }"
                    :wrapperCol="{ span: 19 }"
                    style="transform: translatex(-4px);"
                    v-if="policyFormState.isReservedForSME === '0'"
                    :rules="[{
                      required: true,
                      message: '请选择不适宜原因',
                      trigger: 'change',
                    }]"
                  >
                    <a-select
                      v-model:value="policyFormState.unsuitableReason"
                      :placeholder="isDetailMode ? '' : '请选择'"
                      allow-clear
                      mode="multiple"
                      style="width: 100%"
                      :disabled="isDetailMode"
                    >
                      <a-select-option
                        v-for="option in unsuitableReasonOptions"
                        :value="option.value"
                        :key="option.value"
                      >{{
                        option.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-card>
          <div class="white-space"></div>
          <a-card :bordered="false">
            <template #title>
              <base-cardtitle
                title="其他政策执行信息"
                size="small"
                :divider-visible="false"
              />
            </template>
            <div class="grid grid-cols-1 py-2 pl-16 md:grid-cols-2 lg:grid-cols-3 gap-y-2">
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否紧急项目：</span>
                <a-radio-group
                  v-model:value="policyFormState.isUrgentProject"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否高效科研仪器设备采购：</span>
                <a-radio-group
                  v-model:value="policyFormState.isEfficientProcurement"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否涉及进口产品采购：</span>
                <a-radio-group
                  v-model:value="policyFormState.isImportProduct"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否棚改项目：</span>
                <a-radio-group
                  v-model:value="policyFormState.isRenovationProject"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
              <div class="flex items-center mb-2">
                <span class="mr-1 text-red-500">*</span>
                <span class="mr-2 text-gray-600 whitespace-nowrap">是否政府购买服务：</span>
                <a-radio-group
                  v-model:value="policyFormState.isGovPurchaseService"
                  size="small"
                  :disabled="isDetailMode"
                >
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </div>
            </div>
          </a-card>
          <div class="white-space"></div>
        </div>
        <div
          v-show="activeTabKey === 'attachmentInfo'"
          class="animate__animated animate__fadeIn animate__faster"
        >
          <file-upload-table
            :attachment-types="attachmentTypes"
            v-model:uploaded-files="uploadedFiles"
            :disabled="isDetailMode"
          />
        </div>
      </div>
    </div>


    <!-- 代理机构选择弹窗 -->
    <agency-selector
      v-model:visible="agencySelectorVisible"
      @select="handleAgencySelected"
    />

    <!-- 采购目录选择弹窗 -->
    <purchase-catalog-selector
      v-model:visible="purchaseCatalogSelectorVisible"
      :tree-data="purchaseCatalogOptions"
      :select-list="formState.purchaseCatalogVal"
      @select="handlePurchaseCatalogSelected"
    />

    <!-- 变更原因弹窗 -->
    <reason-modal
      v-model:visible="reasonModalVisible"
      type="change"
      :project-name="formState.projectName"
      :project-id="projectId"
      @confirm="handleChangeReasonConfirm"
    />
  </div>
</template>

<script setup>
import { SearchOutlined, EyeOutlined, SaveOutlined, CheckOutlined } from '@ant-design/icons-vue';
import { frontTip, popConfirm } from "@/common/js/pub-methods.js";
import AgencySelector from '../components/AgencySelector.vue';
import PurchaseCatalogSelector from '../components/PurchaseCatalogSelector.vue';
import ReasonModal from '../components/ReasonModal.vue';
import {
  PURCHASE_METHOD_OPTIONS,
  YEAR_OPTIONS,
  ORGANIZATION_OPTIONS,
  YES_NO_OPTIONS,
  SME_DISCOUNT_RATE_OPTIONS,
  UNSUITABLE_REASON_OPTIONS
} from '@/common/js/global.js';
import { useUserStore } from "@/stores/useUserStore.js";
import { computed } from 'vue';
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
// 当前详情页的操作类型选中值: 新增:add  编辑:edit 详情:detail 变更:change
const pageType = ref('');
// 操作类型map
const typeMap = {
  'add': '新增',
  'edit': '编辑',
  'detail': '详情',
  'change': '变更'
}

// 选项数据集中管理
// 年度选项
const yearOptions = ref(YEAR_OPTIONS);

// 实施方式选项
const purchaseMethodOptions = ref(PURCHASE_METHOD_OPTIONS);

// 组织形式选项
const organizationOptions = ref(ORGANIZATION_OPTIONS);

// 中小企业预留份额措施选项
const smeDiscountRateOptions = ref(SME_DISCOUNT_RATE_OPTIONS);

// 不适宜原因选项
const unsuitableReasonOptions = ref(UNSUITABLE_REASON_OPTIONS);

// 是否选项（通用）
const yesNoOptions = ref(YES_NO_OPTIONS);

const pageTypeTagStyle = computed(() => {
  const colorMap = {
    'add': 'success',
    'edit': 'processing',
    'detail': '',
    'change': 'warning'
  }
  return colorMap[pageType.value] || '';
})

// 判断是否为详情模式
const isDetailMode = computed(() => pageType.value === 'detail');

// 获取路由中的项目ID
const projectId = computed(() => route.params.id || '');

// 当前激活的标签页
const activeTabKey = ref('basicInfo');

// 表单数据
const formState = reactive({
  projectName: '',
  projectCode: '',
  year: '2024',
  purchaseMethod: undefined,
  purchaseUnitInfo: '',
  financialDivision: '',
  prjType: '',
  agentUnit: '',
  agentUnitCode: '', // 代理机构统一社会信用代码
  organization: undefined,
  purchaseCatalogVal: undefined,  //选中的目录值
  budgetMoney: null,
  planDate: null,
  // 项目属性信息
  isProjectPurchase: false,
  isCollectAndSignPrj: false,
  isPurAndMoreYear: false,
  is832PurPrj: false,
  isTenderLawProject: false,
  isPPPProject: false,
  isSMEProject: false,
  isSMEProjecisOldSystemProjectt: false,
  isConfidentialProject: false,
});

// 表单验证规则
const basicFormRules = {
  projectName: [{ required: true, message: '请输入采购项目名称', trigger: 'blur' }],
  organization: [{ required: true, message: '请选择组织形式', trigger: 'change' }],
  purchaseCatalogVal: [{ required: true, message: '请选择采购目录', trigger: 'change' }],
  budgetMoney: [{ required: true, message: '请输入采购金额', trigger: 'change' }],
  prjType: [{ required: true, message: '请输入项目类别', trigger: 'blur' }],
  planDate: [{ required: true, message: '请选择计划采购日期', trigger: 'change' }],
};

// 政策执行信息表单数据
const policyFormState = reactive({
  // 中小企业政策执行信息
  isReservedForSME: '1',
  smeDiscountRate: '3',
  unsuitableReason: [],
  // 其他政策执行信息
  isUrgentProject: false,
  isEfficientProcurement: false,
  isImportProduct: false,
  isRenovationProject: false,
  isGovPurchaseService: false
});

// 政策表单验证规则
const policyFormRules = {
  isReservedForSME: [{ required: true, message: '请选择是否适宜中小企业', trigger: 'change' }],
  smeDiscountRate: [{ required: true, message: '请选择中小企业预留份额措施', trigger: 'change' }],
  unsuitableReason: [{
    required: true,
    message: '请选择不适宜原因',
    trigger: 'change',
  }],
};

// 表单引用
const basicFormRef = ref(null);
const policyFormRef = ref(null);


// 代理机构选择弹窗相关状态
const agencySelectorVisible = ref(false);

// 采购目录选择弹窗相关状态
const purchaseCatalogSelectorVisible = ref(false);

// 变更原因弹窗相关状态
const reasonModalVisible = ref(false);

// 打开采购目录选择弹窗
const openPurchaseCatalogSelector = (e) => {

  purchaseCatalogSelectorVisible.value = true;
};
// 处理采购目录选择
const handlePurchaseCatalogSelected = ({ title = '', key = '' }) => {
  // 可以显示多个 直用逗号字符串隔开 title用数组存储展示
  let formattedValue = key + '-' + title;
  formState.purchaseCatalogVal = formattedValue;
  purchaseCatalogSelectorVisible.value = false;
  frontTip('success', '采购目录选择成功');
  console.log(formState.purchaseCatalogVal, 'xxx');
  // 每次选择完手动触发目录表单字段验证，防止验证失败
  if (basicFormRef.value) basicFormRef.value.validateFields('purchaseCatalogVal')
};

// 返回上一页
const handleBack = () => {
  // 如果是详情页则直接返回
  if (isDetailMode.value) {
    router.back();
  } else {
    popConfirm(
      () => {
        router.back();
      },
      'warn', '提示', '请确认,离开之前是否保存了修改？'
    );
  }

};
// 附件类型列表 - 从后端获取的附件类型数据
const attachmentTypes = ref([
  {
    id: '1',
    attachmentName: '采购项目政府采购预算表',
    uploadFormat: '[doc,docx,zip,excel,pdf]',
    required: true
  },
  {
    id: '2',
    attachmentName: '采购需求调查报告',
    uploadFormat: '[doc,docx,zip,pdf]',
    required: true
  },
  {
    id: '3',
    attachmentName: '三重一大会议纪要',
    uploadFormat: '[doc,docx,zip,pdf]',
    required: true
  },
  {
    id: '4',
    attachmentName: '其他文件',
    uploadFormat: '[doc,,docx,zip,pdf]',
    required: false
  },
]);

// 已上传的附件列表 - 用于存储已上传的文件信息
const uploadedFiles = ref([
  // 示例数据，实际应该从后端获取
  {
    attachmentTypeId: '1',
    fileId: '101',
    fileName: '2024年度采购预算.pdf',
    fileSize: '2.5MB',
    uploadTime: '2024-05-20 10:30:45',
    uploader: '张三'
  }
]);

// 检查必传附件是否都已上传
const checkRequiredAttachments = () => {
  // 获取所有必传附件类型
  const requiredTypes = attachmentTypes.value.filter(type => type.required);

  // 检查每个必传类型是否都有对应的已上传文件
  const allUploaded = requiredTypes.every(type =>
    uploadedFiles.value.some(file => file.attachmentTypeId === type.id)
  );

  console.log('必传附件是否都已上传:', allUploaded);
  return allUploaded;
};
// 监听上传文件列表变化
watch(() => uploadedFiles.value, (newFiles) => {
  console.log('上传文件列表已更新:', newFiles);
  // 检查是否所有必传附件都已上传
  checkRequiredAttachments();
}, { deep: true });



// 打开代理机构选择弹窗
const openAgencySelector = () => {
  agencySelectorVisible.value = true;
};

// 处理代理机构选择
const handleAgencySelected = (agency) => {
  formState.agentUnit = agency.agencyName;
  formState.agentUnitCode = agency.socialCreditCode;
  agencySelectorVisible.value = false;
  frontTip('success', '代理机构选择成功');
};




// 初始化页面
const initPage = () => {
  const { operateType = '', entrance = '' } = route.query;
  // 页面操作类型
  pageType.value = operateType;
  // 当前实施方式
  let curMethodItem = PURCHASE_METHOD_OPTIONS.find(i => i.value === entrance);
  formState.purchaseMethod = curMethodItem?.value || '';
  //采购项目编号（系统备案后自动获取）
  formState.projectCode = 'P2023050001';
  //财政区划 单位信息 从userStore中对应获取
  formState.purchaseUnitInfo = userStore.getUserName;
  formState.financialDivision = userStore.getFinancialDivision || '长沙市';
  // 当页面操作为详情 编辑 变更的时候 需要请求数据 为当前页面表单附件赋初始值
  if (pageType.value === 'detail' || pageType.value === 'edit' || pageType.value === 'change') {
    reqInitFormAndAttachInfo()
  }
};
// 初始接口数据获取
const reqInitFormAndAttachInfo = async () => {

}

// 提交按钮
const handleSubmit = () => {
  popConfirm(
    async () => {
      // 验证表单完整性
      const isValid = await validateForms();
      if (isValid) {
        // 变更类型，显示变更原因弹窗
        if (pageType.value === 'change') {
          reasonModalVisible.value = true;
        } else {
          // 其他类型直接提交  先保存在提交 
          // await saveInfo
          submitForm();
        }
      }
    },
    'warn', '确认', '确定要提交当前项目信息吗？提交后将进入审批流程。'
  );
};

// 统一的表单提交处理
const submitForm = () => {
  // 构建提交数据，包含表单数据和附件信息
  let catalogOptions = formState.purchaseCatalogVal.map(item => {
    let res = item.split('-');
    return {
      name: res[1],
      code: res[0]
    }
  })
  const submitData = {
    // 基本信息
    basicInfo: { ...formState, catalogOptions },
    // 政策信息
    policyInfo: { ...policyFormState },
    // 附件信息 - 转换为后端需要的格式
    attachments: uploadedFiles.value.map(file => ({
      attachmentTypeId: file.attachmentTypeId,
      fileId: file.fileId,
      fileName: file.fileName
    }))
  };

  console.log('提交数据:', submitData);

  // 这里应该调用实际的提交API
  // 模拟提交成功
  frontTip('success', '提交成功');
  router.back();
};

// 处理变更原因确认
const handleChangeReasonConfirm = (data) => {
  // 处理变更原因数据
  console.log('变更原因:', data.reason);
  // 这里应该调用实际的变更API
  // await changeProject(projectId.value, data.reason);
  frontTip('success', '变更申请提交成功');
  router.back();
};

// 采购目录树形数据
const purchaseCatalogOptions = ref([
  {
    name: 'A 货物',
    value: 'A',
    key: 'A',
    disableCheckbox: true, // 禁用多选框
    children: [
      {
        name: 'A020000C0 设备',
        value: 'A020000C0',
        key: 'A020000C0',
        disableCheckbox: true, // 禁用多选框
        children: [
          {
            name: '********* 信息化设备',
            value: '*********',
            key: '*********',
            disableCheckbox: true, // 禁用多选框
            children: [
              {
                name: '********* 计算机',
                value: '*********',
                key: '*********',
                children: [
                  {
                    name: '巨型计算机',
                    value: 'A02010101',
                    key: 'A02010101',
                    isLeaf: true,
                    purchaseType: '分散采购'
                  },
                  {
                    name: '大型计算机',
                    value: 'A02010102',
                    key: 'A02010102',
                    isLeaf: true,
                    purchaseType: '分散采购'
                  },
                  {
                    name: '中型计算机',
                    value: 'A02010103',
                    key: 'A02010103',
                    isLeaf: true,
                    purchaseType: '分散采购'
                  },
                  // {
                  //   name: '服务器',
                  //   value: 'A02010104',
                  //   key: 'A02010104',
                  //   isLeaf: true,
                  //   purchaseType: '政府集中采购'
                  // },
                  {
                    name: '小型计算机',
                    value: 'A02010105',
                    key: 'A02010105',
                    isLeaf: true,
                    purchaseType: '分散采购'
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  {
    name: 'B 工程',
    value: 'B',
    key: 'B',
    disableCheckbox: true, // 禁用多选框
    children: [
      {
        name: '建筑工程',
        value: 'B01',
        key: 'B01',
        children: [
          {
            name: '房屋建筑',
            value: 'B0101',
            key: 'B0101',
            isLeaf: true,
            purchaseType: '政府集中采购'
          },
          {
            name: '市政工程',
            value: 'B0102',
            key: 'B0102',
            isLeaf: true,
            purchaseType: '分散采购'
          }
        ]
      }
    ]
  },
  {
    name: 'C 服务',
    value: 'C',
    key: 'C',
    disableCheckbox: true, // 禁用多选框
    children: [
      {
        name: 'IT服务',
        value: 'C01',
        key: 'C01',
        children: [
          {
            name: '软件开发',
            value: 'C0101',
            key: 'C0101',
            isLeaf: true,
            purchaseType: '政府集中采购'
          },
          {
            name: '系统集成',
            value: 'C0102',
            key: 'C0102',
            isLeaf: true,
            // purchaseType: '分散采购'
          }
        ]
      }
    ]
  }
]);

// 验证表单
const validateForms = async () => {
  try {
    // 验证基本信息表单
    await basicFormRef.value.validate();

    // 验证政策信息表单
    await policyFormRef.value.validate();

    // 验证必传附件是否都已上传
    const attachmentsValid = checkRequiredAttachments();
    if (!attachmentsValid) {
      activeTabKey.value = 'attachmentInfo';
      frontTip('info', '必传附件未上传完整，请检查');
      return false;
    }

    return true;
  } catch (error) {
    if (error.errorFields && error.errorFields.length > 0) {
      // 确定哪个标签页有错误
      const basicInfoHasError = error.errorFields.some(field =>
        Object.keys(basicFormRules).includes(field.name[0]));
      const policyInfoHasError = error.errorFields.some(field =>
        Object.keys(policyFormRules).includes(field.name[0]));
      if (basicInfoHasError) {
        activeTabKey.value = 'basicInfo';
        basicFormRef.value.scrollToField()
        frontTip('info', '项目基本信息未填写完整，请检查');
      } else if (policyInfoHasError) {
        activeTabKey.value = 'policyInfo';
        policyFormRef.value.scrollToField()
        frontTip('info', '政策执行信息未填写完整，请检查');
      }
    }
    return false;
  }
};
// 流程查看
const handleViewProcess = () => {
  frontTip('info', '功能还在开发中... ');
};
// 保存按钮
const handleSave = () => {
  popConfirm(
    async () => {
      // 保存操作，不验证表单完整性
      frontTip('success', '保存成功');
    },
    'save'
  );
};

// 在组件挂载后加载数据
onMounted(() => {
  initPage();
});


</script>

<style lang="scss">
.white-space {
  height: 8px;
  background-color: $primary-bg-color;
}

.ant-select-multiple .ant-select-selection-item-remove svg {
  display: block;
}
</style>