ub<template>
  <div id="login-container">
    <LoginHeader />
    <div class="login-card_wrapper">
      <div class="left-img"></div>
      <div class="right-form">
        <h1 class="title">用户登录</h1>
        <a-form
          :model="formState"
          name="login_form"
          @finish="handleLogin"
          @finishFailed="onFinishFailed"
        >
          <a-tabs v-model:activeKey="activeKey">
            <a-tab-pane
              key="ca"
              tab="CA登录"
              v-if="IS_CA_LOGIN"
            >
              <a-form-item
                :name="['ca', 'account']"
                :rules="[{ required: true, message: '请插入CA' }]"
              >
                <div style="display: flex;align-items: flex-end;">
                  <a-input
                    v-model:value="formState.ca.account"
                    placeholder="请插入CA"
                    disabled
                    class="refresh-input"
                    size="large"
                  >
                    <template #prefix>
                      <span class="icon-user"></span>
                    </template>
                  </a-input>
                  <a-button
                    size="large"
                    type="primary"
                    style="width: 48px;"
                    @click="initCa"
                  ><template #icon>
                      <span class="icon-refresh"></span>
                    </template></a-button>
                </div>
              </a-form-item>
              <a-form-item
                :name="['ca', 'password']"
                :rules="[{ required: true, message: '请输入CA密码' }]"
              >
                <a-input-password
                  v-model:value="formState.ca.password"
                  placeholder="请输入CA密码"
                  size="large"
                >
                  <template #prefix>
                    <span class="icon-pwd"></span>
                  </template>
                </a-input-password>
              </a-form-item>
            </a-tab-pane>
            <a-tab-pane
              key="normal"
              tab="账号密码登录"
            >
              <a-form-item
                :name="['normal', 'username']"
                :rules="[{ required: true, message: '请输入账号' }]"
              >
                <a-input
                  v-model:value="formState.normal.username"
                  placeholder="请输入账号/手机号码"
                  allowClear
                  size="large"
                >
                  <template #prefix>
                    <span class="icon-user"></span>
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item
                :name="['normal', 'password']"
                :rules="[{ required: true, message: '请输入密码' }]"
              >
                <a-input-password
                  v-model:value="formState.normal.password"
                  placeholder="请输入密码"
                  size="large"
                >
                  <template #prefix>
                    <span class="icon-pwd"></span>
                  </template>
                </a-input-password>
              </a-form-item>
              <a-form-item
                :name="['normal', 'verifyPicCode']"
                :rules="[{ required: true, message: '请输入验证码' }]"
              >
                <div
                  class="flex justify-between"
                  style="gap: 20px;"
                >
                  <a-input
                    v-model:value="formState.normal.verifyPicCode"
                    allowClear
                    placeholder="请输入验证码"
                    size="large"
                  >
                    <template #prefix>
                      <span class="icon-verify_code"></span>
                    </template>
                  </a-input>
                  <img
                    :src="picCode"
                    @click="getImageCodes"
                    style="width: 160px;height: 40px;border-radius: 4px;"
                  >
                </div>
              </a-form-item>
              <!-- <a-form-item name="rememberMe">
                <a-checkbox v-model:checked="formState.rememberMe">记住我</a-checkbox>
              </a-form-item> -->
            </a-tab-pane>

          </a-tabs>
          <a-form-item>
            <a-button
              :loading="loginLoading"
              type="primary"
              html-type="submit"
              class="login-form-button"
              style="width: 100%;"
              size="large"
            >
              登录
            </a-button>
          </a-form-item>
          <div
            class="flex-between link-group"
            v-if="activeKey === 'ca'"
          >
            <a
              :href="CA_PUBLIC_PLATFORM_URL"
              target="_blank"
            >办理CA</a>
            <a
              :href="CA_DIRIVE_URL"
              style="font-size: inherit;"
            >CA驱动下载</a>
          </div>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import LoginHeader from "@/layout/header/login-header.vue";
import { frontTip } from "@/common/js/pub-methods.js"
import { sessionCache } from "@/utils/cache.js"
import { CA_DIRIVE_URL, CA_PUBLIC_PLATFORM_URL } from "@/common/js/global.js"
import { useUserStore } from "@/stores/useUserStore.js";
import { loadCaUser, caLoginCallback } from "@/utils/ca-login.js";
import { useGlobalCacheStore } from "@/stores/useGlobalCacheStore.js";
const userStore = useUserStore();
const router = useRouter();
const globalCacheStore = useGlobalCacheStore();
// 活动的标签页
const IS_CA_LOGIN = import.meta.env.VITE_APP_CA_LOGIN === '1';
const activeKey = ref('normal');
const picCode = ref("");
const loginLoading = ref(false);
const formState = reactive({
  normal: {
    username: '',
    password: '',
    verifyPicCode: '',
    rememberMe: false
  },
  ca: {
    status: false,
    account: '',
    password: ''
  }
});
// 处理登录失败
const onFinishFailed = errorInfo => {
  frontTip('error', '登录失败，请稍后重试', '登录表单验证失败:' + JSON.stringify(errorInfo));
};
// 账号登录处理
const handleLogin = async (values) => {
  try {
    loginLoading.value = true;
    if (activeKey.value === 'normal') {
      let n_res = await reqNormalLogin(values);
      if (!n_res) {
        getImageCodes();
        frontTip('error', '登录失败，请检查用户名或密码是否正确');
        return;
      }
    } else if (activeKey.value === 'ca') {
      let c_res = await hanleCALogin(values);
      if (!c_res) return; //错误交给封装函数内抛出
    }
    // 登录成功后异步拉取全局缓存并跳转首页
    globalCacheStore.fetchGlobalCacheData();
    router.replace('/workplace');
  } catch (error) {
    frontTip('error', '登录失败，请稍后重试', error);
    // 刷新验证码
    getImageCodes();
  } finally {
    loginLoading.value = false;
  }
};
// 普通登录
const reqNormalLogin = async (values) => {
  const { username, password, verifyPicCode } = values.normal;
  // 模拟验证码校验 实际校验接口在后台
  const savedCode = sessionCache.get('verify_code');
  if (!savedCode || verifyPicCode.toUpperCase() !== savedCode) {
    frontTip('error', '验证码错误，请重新输入');
    // // 刷新验证码
    // getImageCodes();
    return;
  }
  const res = await userStore.login({
    username,
    password,
    verifyPicCode
  });
  return res;
}
//ca登录

// 对ca用户进行操作
const caUserInfo = reactive({
  realName: '',
  certId: '',
  params: {
    UserCert: '',
    strRandom: '',
    UserSignedData: ''
  }
})
const initCa = async () => {
  const caUser = await loadCaUser();
  if (!caUser) return;
  caUserInfo.realName = caUser.realName;
  caUserInfo.certId = caUser.certId;
}

const hanleCALogin = async (values) => {
  const { account, password } = values.ca;
  let timeBegin = new Date();
  // 检验证书口令
  SOF_Login(caUserInfo.certId, password, reqCALogin, { begin: timeBegin, certId: caUserInfo.certId });
}
const reqCALogin = async (retObj) => {
  const caResult = await caLoginCallback(retObj);
  if (!caResult) return;
  const { userCert, strRandom, userSignedData } = caResult;
  caUserInfo.params = {
    UserCert: userCert,
    strRandom: strRandom,
    UserSignedData: userSignedData
  }
  const res = await userStore.calogin({
    ...caUserInfo.params
  });
  if (!res) return;
  return res;
}
// 获取验证码
const getImageCodes = async () => {
  // 模拟验证码生成
  const createRandomCode = () => {
    // 创建一个Canvas元素用于绘制验证码
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    // 设置Canvas大小
    canvas.width = 160;
    canvas.height = 40;

    // 填充白色背景
    context.fillStyle = '#e7ebf0';
    context.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制干扰线
    for (let i = 0; i < 3; i++) {
      context.strokeStyle = `rgb(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255})`;
      context.beginPath();
      context.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
      context.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
      context.stroke();
    }

    // 绘制干扰点
    for (let i = 0; i < 50; i++) {
      context.fillStyle = `rgb(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255})`;
      context.beginPath();
      context.arc(Math.random() * canvas.width, Math.random() * canvas.height, 1, 0, 2 * Math.PI);
      context.fill();
    }

    // 绘制验证码文字
    const code = Math.random().toString(36).substr(2, 4).toUpperCase();
    context.font = 'bold 24px Arial';
    context.textAlign = 'center';
    context.textBaseline = 'middle';

    // 每个字符有不同颜色和角度
    for (let i = 0; i < code.length; i++) {
      context.fillStyle = `rgb(${Math.random() * 200}, ${Math.random() * 200}, ${Math.random() * 200})`;
      context.save();
      context.translate(40 * i + 20, 20);
      context.rotate((Math.random() - 0.5) * 0.3);
      context.fillText(code[i], 0, 0);
      context.restore();
    }

    // 保存验证码到会话存储中用于校验
    sessionCache.set('verify_code', code);

    // 返回Canvas的DataURL
    return canvas.toDataURL('image/png');
  };

  // const res = await getPicCode();
  // if (res && !res.result_code) {
  //   let blob = new Blob([res], { type: "image/png" });
  //   let url = URL.createObjectURL(blob);
  //   picCode.value = url;
  //   } else {
  //   frontTip('error', res.result_message || '验证码加载失败,请点击刷新')
  // }

  // 设置验证码图片
  picCode.value = createRandomCode();
};

onMounted(() => {
  // 初始化生成验证码
  getImageCodes();
  if (IS_CA_LOGIN) {
    initCa();
    // 监听检查ukey是否插入,即时更新数据
    SetOnUsbKeyChangeCallBack(initCa)
  }
})
</script>

<style lang="scss" scoped>
#login-container {
  width: 100%;
  height: 100%;
  /* 需要一个蓝白渐变色 */
  @include bg('bg-login_new.png');
  background-size: 100% 100%;

  padding-top: 6.5vh;

  .login-card_wrapper {
    width: 908px;
    height: 494px;
    margin: 0 auto;
    background-color: #fff;
    border: 10px solid #e3eff7;
    box-shadow: 0 0 20px 0 #29419333;
    margin-top: 16px;
    border-radius: 22px;
    @include bg('bg-logincard_new.png');
    overflow: hidden;
    display: flex;


    .left-img {
      width: 49%;
      height: 100%;
      // background: url(@/assets/images/bg/bg-login_card.png) no-repeat;
      background-size: 100% 100%;
    }

    .right-form {
      width: 51%;
      padding: 36px 48px;

      .title {
        font-size: 22px;
        font-family: $font-title;
        /* font-weight: bold; */
        margin: 8px 0;
      }

      .ant-tabs {
        .ant-tabs-tabpane {
          padding: 8px 2px 2px 2px;

          .ant-form {
            .ant-form-item {
              :deep(.ant-btn.login-form-button) {
                >span {
                  font-size: 16px;
                  font-family: $font-title;
                }
              }

              &:last-child {
                margin-bottom: 4px;
              }

              :deep(.ant-input-affix-wrapper:not(.ant-input-affix-wrapper:hover, .ant-input-affix-wrapper:focus, .ant-input-affix-wrapper-focused)) {
                border-color: #B2B2B2;
              }
            }

          }
        }
      }

    }
  }

  .link-group {
    a {
      color: $primary-color ;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  :deep(.ant-input-affix-wrapper-lg) {
    padding: 6.5px 11px;
    /* border-color: #c0c0c0; */
  }

  :deep(.ant-input) {
    padding-left: 11px;
    font-size: 15px;
  }

  :deep(.ant-form-item-explain-error) {
    font-size: 13px
  }

  :deep(.ant-tabs .ant-tabs-tab-btn) {
    font-size: 16px;
  }
}

.icon-refresh {
  @include base-icon(24px, 'icon-refresh_btn.svg');
}

.icon-user {
  @include base-icon(22px, 'icon-user_avatar.svg');
}

.icon-pwd {
  @include base-icon(20px, 'icon-pwd_locked.svg');
}

.icon-verify_code {
  @include base-icon(20px, 'icon-verify_code.svg');
}

@media screen and (min-width: 1600px) {
  #login-container {
    padding-top: 9vh;

    .login-card_wrapper {
      width: 1008px;
      height: 550px;
      margin-top: 20px;
    }
  }
}
</style>