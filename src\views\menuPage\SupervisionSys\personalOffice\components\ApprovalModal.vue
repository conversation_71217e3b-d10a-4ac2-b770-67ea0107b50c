<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="modalTitle"
    :maskClosable="false"
    :width="500"
    centered
    class="approval-modal"
  >
    <template #icon>
      <component :is="modalIcon" />
    </template>
    <div class="px-2 animate__animated animate__fadeIn animate_faster">
      <div class="flex items-start mb-4">
        <div class="flex-shrink-0 mr-3">
          <div
            class="flex items-center justify-center w-10 h-10 rounded-full shadow-sm"
            :class="type === 'reject' ? 'bg-red-50' : 'bg-green-50'"
          >
            <CloseCircleOutlined
              v-if="type === 'reject'"
              class="text-xl text-red-500"
            />
            <CheckCircleOutlined
              v-else
              class="text-xl text-green-500"
            />
          </div>
        </div>
        <div class="flex-1 animate__animated animate__fadeInRight animate_faster">
          <h3
            class="text-base font-medium"
            :class="type === 'reject' ? 'text-red-500' : 'text-green-500'"
          >
            {{ type === 'reject' ? '您确认要驳回该采购方案吗？' : '您确认要通过该采购方案吗？' }}
          </h3>
          <p class="mt-1 text-gray-600 break-all">
            {{ type === 'reject' ? '驳回后，项目将退回到提交人员进行修改。' : '通过后，项目将进入下一流程阶段。' }}
          </p>
        </div>
      </div>

      <a-form
        layout="vertical"
        ref="formRef"
        class="animate__animated animate__fadeIn animate_faster"
      >
        <a-form-item
          :label="type === 'reject' ? '驳回原因' : '审批意见'"
          :required="type === 'reject'"
        >
          <a-textarea
            v-model:value="comment"
            :placeholder="type === 'reject' ? '请输入驳回原因（必填）' : '请输入审批意见（选填）'"
            :rows="4"
            :maxlength="200"
            show-count
            allow-clear
            :class="type === 'reject'
              ? 'border-red-300 focus:border-red-500 hover:border-red-400 transition-all duration-300'
              : 'border-green-300 focus:border-green-500 hover:border-green-400 transition-all duration-300'"
          />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <div class="flex justify-end space-x-2">
        <a-button
          @click="handleCancel"
          :disabled="loading"
        >
          取消
        </a-button>
        <a-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :class="type === 'reject' ? 'bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600 focus:bg-red-600 focus:border-red-600' : 'bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600 focus:bg-green-600 focus:border-green-600'"
        >
          {{ type === 'reject' ? '确认驳回' : '确认通过' }}
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { frontTip } from "@/common/js/pub-methods.js";
import { h } from 'vue';
import {
  CloseCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'approve', // 'approve' 或 'reject'
  },
  projectId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

// 内部弹窗状态
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单引用
const formRef = ref(null);
// 意见/原因值
const comment = ref('');
// 加载中状态
const loading = ref(false);

// 计算属性获取标题
const modalTitle = computed(() => {
  return props.type === 'reject' ? '驳回确认' : '审批通过确认';
});

// 获取图标
const modalIcon = computed(() => {
  return props.type === 'reject'
    ? h(CloseCircleOutlined, { style: 'color: #ff4d4f' })
    : h(CheckCircleOutlined, { style: 'color: #52c41a' });
});

// 取消操作
const handleCancel = () => {
  comment.value = '';
  modalVisible.value = false;
};

// 提交操作
const handleSubmit = () => {
  // 表单验证 - 驳回时必须填写原因
  if (props.type === 'reject' && !comment.value.trim()) {
    frontTip('info', '请输入驳回原因');
    return;
  }

  // 设置加载状态
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    emit('confirm', {
      id: props.projectId,
      comment: comment.value,
      type: props.type
    });

    // 重置状态
    loading.value = false;
    comment.value = '';
    modalVisible.value = false;
  }, 1000);
};

// 监听弹窗打开时重置状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    comment.value = '';
    loading.value = false;
  }
});
</script>

<style lang="scss" scoped></style>