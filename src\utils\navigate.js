import router from '@/router';
import { sessionCache } from '@/utils/cache';
import { useTabsStore } from '@/stores/useTabsStore';
import { DEFAULT_MENU_ITEM } from "@/common/js/global.js"
import { frontTip } from "@/common/js/pub-methods.js"
/**
 * TODO菜单，标签页或其他操作导航服务
 */
class NavigateService {
  // 基本配置
  config = {
    // 菜单列表缓存键
    menuListCacheKey: 'menuList',
    // 激活菜单缓存键
    activeMenuCacheKey: 'activeTab',
    // 工作台菜单代码
    workplaceMenuCode: DEFAULT_MENU_ITEM.menuId,
    // 工作台名字
    workplaceMenuName: DEFAULT_MENU_ITEM.menuName,
    // 工作台路径
    workplacePath: DEFAULT_MENU_ITEM.path
  }

  /**
   * 根据菜单代码导航
   * @param {string} menuCode 菜单代码
   * @param {Object} query 查询参数
   * @param {boolean} replace 是否替换当前历史记录
   * @returns {Promise<boolean>} 导航结果
   */
  navigateByMenuCode (menuCode, query = {}, replace = false) {
    if (!menuCode) return Promise.resolve(false);
    try {
      // 工作台特殊处理
      if (menuCode === this.config.workplaceMenuCode) {
        this.setActiveMenu(menuCode);
        this.addToTabs(menuCode, this.config.workplaceMenuName);
        return router.push(this.config.workplacePath);
      }

      // 直接从路由表中查找菜单路径
      const route = this.findRouteByMenuCode(menuCode);

      if (!route) {
        frontTip('error', '未找到该菜单！请联系管理员');
        router.push('/404');
        return Promise.resolve(false);
      }

      // 设置菜单选中状态并添加标签页
      this.setActiveMenu(menuCode);
      this.addToTabs(menuCode, route.meta?.title || '');

      // 执行导航
      return replace
        ? router.replace({ path: route.path, query })
        : router.push({ path: route.path, query });
    } catch (error) {
      console.error('导航错误:', error);
      return Promise.resolve(false);
    }
  }

  /**
   * 设置当前活动菜单
   * @param {string} menuCode 菜单代码
   */
  setActiveMenu (menuCode) {
    if (menuCode) sessionCache.set(this.config.activeMenuCacheKey, menuCode);
  }

  /**
   * 添加到标签页
   * @param {string} menuCode 菜单代码
   * @param {string} menuName 菜单名称
   */
  addToTabs (menuCode, menuName) {
    if (!menuCode || !menuName) return;
    try {
      const tabsStore = useTabsStore();
      tabsStore?.addTab({ menuCode, menuName });
    } catch (error) {
      console.error('添加标签页失败:', error);
    }
  }

  /**
   * 导航到工作台
   * @returns {Promise<boolean>} 导航结果
   */
  navigateToWorkplace () {
    return this.navigateByMenuCode(this.config.workplaceMenuCode);
  }

  /**
   * 根据路径查找菜单码
   * @param {string} path 路径
   * @returns {string|null} 菜单代码
   */
  findMenuCodeByPath (path) {
    if (!path) return null;
    // 规范化路径
    const normalizedPath = this.normalizePath(path);
    // 工作台特殊处理
    if (normalizedPath === this.config.workplacePath) {
      return this.config.workplaceMenuCode;
    }
    // 从路由表中查找匹配的路由
    const routes = router.getRoutes();
    // 1. 尝试精确匹配路径
    const exactRoute = routes.find(r => r.path === normalizedPath);
    if (exactRoute?.meta?.menuCode) {
      return exactRoute.meta.menuCode;
    }
  }

  /**
   * 规范化路径
   * @param {string} path 原始路径
   * @returns {string} 规范化后的路径
   */
  normalizePath (path) {
    if (!path) return '';
    return (path.startsWith('/') ? path : `/${path}`).replace(/\/+$/, '');
  }

  /**
   * 根据菜单代码查找路由配置
   * @param {string} menuCode 菜单代码
   * @returns {Object|null} 路由配置
   */
  findRouteByMenuCode (menuCode) {
    if (!menuCode) return null;

    // 工作台特殊处理
    if (menuCode === this.config.workplaceMenuCode) {
      return router.getRoutes().find(route =>
        route.path === this.config.workplacePath
      );
    }

    // 直接从路由表中查找带有对应menuCode的路由
    return router.getRoutes().find(route =>
      route.meta?.menuCode === menuCode
    );
  }

}

// 导出单例实例
export const navigateService = new NavigateService();

// 创建Vue插件
export const NavigatePlugin = {
  install (app) {
    app.config.globalProperties.$navigate = navigateService;
  }
};

export default navigateService;