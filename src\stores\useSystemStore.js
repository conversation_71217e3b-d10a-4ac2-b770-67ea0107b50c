import { defineStore } from 'pinia';
import { sessionCache } from "@/utils/cache.js";
import { useTabsStore } from "./useTabsStore.js"
import { useMenuStore } from "./useMenuStore.js"

export const useSystemStore = defineStore('system', () => {
  // 后期可以增加默认系统
  const systems = ref([]);
  // 当前选中的系统
  const currentSystem = ref({});

  // 可见的子系统列表（受响应式控制）
  const visibleSystems = ref([]);

  // 更多子系统列表（下拉菜单中显示）
  const moreSystems = ref([]);

  // 加载状态
  const loading = ref(false);



  // 从缓存恢复状态
  const initStateFromCache = () => {
    const cachedSystem = sessionCache.getJSON('currentSystem');
    if (cachedSystem && systems.value.length > 0) {
      // 确保缓存的系统在当前系统列表中
      const foundSystem = systems.value.find(sys => sys.id === cachedSystem.id);
      if (foundSystem) {
        currentSystem.value = foundSystem;
      } else {
        currentSystem.value = systems.value[0];
      }
    } else if (systems.value.length > 0) {
      currentSystem.value = systems.value[0];
    }
  };


  // 计算属性
  const getCurrentSystem = computed(() => currentSystem.value);
  const getSystems = computed(() => systems.value);
  const getVisibleSystems = computed(() => visibleSystems.value);
  const getMoreSystems = computed(() => moreSystems.value);
  const isLoadingCompleted = computed(() => loading.value);

  // 设置可见子系统
  const setVisibleAndMoreSystems = (visibleCount) => {
    if (!visibleCount || visibleCount <= 0) return;
    visibleSystems.value = systems.value.slice(0, visibleCount);
    moreSystems.value = systems.value.slice(visibleCount);
  };

  // 根据窗口宽度更新可见系统数量
  const updateVisibleSystemsByWidth = (width) => {
    if (width < 1200) {
      setVisibleAndMoreSystems(2);
    } else if (width < 1300) {
      setVisibleAndMoreSystems(3);
    } else {
      setVisibleAndMoreSystems(4);
    }
  };

  // 切换子系统更新数据
  const switchSystem = async (system) => {
    const targetSystem = systems.value.find(sys => sys.id === system.id);
    if (!targetSystem) return;
    // 更新当前系统
    currentSystem.value = targetSystem;
    sessionCache.setJSON('currentSystem', currentSystem.value);
    // 导航到系统首页
    // 每次切换系统需要重置一些上一个系统的状态，侧边菜单，顶部tab等
    const menuStore = useMenuStore();
    const tabStore = useTabsStore();
    // 重置侧边菜单
    menuStore.resetState();
    // 重置顶部tab
    tabStore.resetState();
    return true;
  };
  // 模拟接口获取系统列表
  const fetchSystems = async () => {
    loading.value = true;
    try {
      // 模拟API请求
      return new Promise((resolve) => {
        setTimeout(() => {
          const systemData = [
            { id: 'main', name: import.meta.env.VITE_APP_TITLE, icon: 'icon-business', logo: "", sortCode: 1 },
            { id: 'sub-sys3', name: '统计分析', icon: 'icon-stats', logo: "", sortCode: 3 },
            { id: 'sub-sys4', name: '系统管理', icon: 'icon-system', logo: "", sortCode: 4 },
            { id: 'sub-sys5', name: '更多系统', icon: 'icon-more', logo: "", sortCode: 5 },
            { id: 'sub-sys2', name: '监管预警', icon: 'icon-warning', logo: "", sortCode: 2 },
          ];
          systems.value = systemData.sort((a, b) => a.sortCode - b.sortCode);
          initStateFromCache();
          resolve(systemData);
        }, 300); // 模拟网络延迟
      });
    } catch (error) {
      console.error('获取系统列表失败:', error);
      return [];
    } finally {
      loading.value = false;
    }
  };
  // 初始化
  const initialize = () => {
    const width = window.innerWidth;
    updateVisibleSystemsByWidth(width);
  };

  // 重置状态
  const resetState = () => {
    visibleSystems.value = [];
    moreSystems.value = [];
    sessionCache.remove('currentSystem');
  };

  return {
    // 状态
    systems,
    currentSystem,
    visibleSystems,
    moreSystems,
    isLoadingCompleted,
    // 计算属性
    getCurrentSystem,
    getSystems,
    getVisibleSystems,
    getMoreSystems,
    // 方法
    fetchSystems,
    setVisibleAndMoreSystems,
    updateVisibleSystemsByWidth,
    switchSystem,
    initialize,
    resetState
  };
}); 