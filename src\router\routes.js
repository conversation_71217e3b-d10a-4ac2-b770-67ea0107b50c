import { DEFAULT_MENU_ITEM } from '@/common/js/global.js';
const Layout = () => import('@/layout/index.vue')
// 路由配置
/**
 * 基础路由，不需要权限校验
 * 这些路由对所有用户可见
 */
export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/loginPage/Login.vue'),
    meta: {
      title: '登录',
      noAuth: true
    }
  },
  // {
  //   path: '/empty',
  //   name: 'Empty',
  //   component: () => import('@/views/emptyPage/empty.vue'),
  //   meta: {
  //     title: '空页面',
  //     noAuth: true
  //   }
  // },
  {
    path: '/401',
    name: '401',
    component: () => import('@/views/errorPage/401.vue'),
    meta: {
      title: '无权限',
      noAuth: true
    }
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/errorPage/403.vue'),
    meta: {
      title: '禁止访问',
      noAuth: true
    }
  },
  {
    path: '/500',
    name: '500',
    component: () => import('@/views/errorPage/500.vue'),
    meta: {
      title: '服务器错误',
      noAuth: true
    }
  },
  {
    path: '/',
    name: 'Root',
    redirect: '/workplace',
    meta: {
      hidden: true
    }
  },
  {
    path: '/workplace',
    name: 'Workplace',
    component: Layout,
    redirect: '/workplace/index',
    children: [
      {
        path: 'index',
        name: 'WorkplaceHome',
        component: () => import('@/views/menuPage/workplace/Workplace.vue'),
        // 导入工作台默认配置
        meta: {
          title: DEFAULT_MENU_ITEM.menuName,
          icon: DEFAULT_MENU_ITEM.icon,
          menuCode: DEFAULT_MENU_ITEM.menuId,
          affix: true
        },
      }
    ]
  }
];

// 匹配所有不存在的路由，必须放在最后
export const notFoundRoute = {
  path: '/:pathMatch(.*)*',
  name: 'NotFound',
  component: () => import('@/views/errorPage/404.vue'),
  meta: {
    noAuth: true,
    hidden: true
  }
}; 