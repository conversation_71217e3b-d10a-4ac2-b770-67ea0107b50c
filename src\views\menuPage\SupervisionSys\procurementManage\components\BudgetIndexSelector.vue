<template>
  <a-modal
    v-model:visible="modalVisible"
    title="选择指标"
    :width="1000"
    :footer="null"
    :mask-closable="false"
    wrap-class-name="budget-index-selector-modal"
  >
    <div class="p-4">
      <!-- 搜索区域 -->
      <div class="flex items-center justify-between gap-6 mb-6">
        <div class="flex gap-4">
          <div class="flex items-center">
            <span class="mr-2 whitespace-nowrap">项目名称：</span>
            <a-input
              v-model:value="searchForm.budgetName"
              placeholder="请输入"
              allow-clear
              style="width: 220px"
            />
          </div>
          <div class="flex items-center">
            <span class="mr-2 whitespace-nowrap">预算年度：</span>
            <a-select
              v-model:value="searchForm.year"
              placeholder="请选择"
              allow-clear
              style="width: 120px"
            >
              <a-select-option value="2024">2024</a-select-option>
              <a-select-option value="2025">2025</a-select-option>
              <a-select-option value="2026">2026</a-select-option>
            </a-select>
          </div>
          <div class="flex items-center">
            <span class="mr-2 whitespace-nowrap">资金性质：</span>
            <a-select
              v-model:value="searchForm.fundNature"
              placeholder="请选择"
              allow-clear
              style="width: 180px"
            >
              <a-select-option value="一般公共预算资金">一般公共预算资金</a-select-option>
              <a-select-option value="政府性基金预算资金">政府性基金预算资金</a-select-option>
              <a-select-option value="国有资本经营预算资金">国有资本经营预算资金</a-select-option>
            </a-select>
          </div>
        </div>
        <a-space :size="12">
          <a-button
            type="primary"
            @click="setFilterOption(searchForm)"
          >查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-space>
      </div>

      <!-- 表格区域 -->
      <div class="bg-white rounded">
        <a-table
          :dataSource="list"
          :columns="columns"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange"
          :scroll="{ x: 1000, y: 360 }"
          row-key="id"
          :custom-row="handleCustomRow"
          :row-selection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'budgetName'">
              <div class="break-all whitespace-normal">{{ record.budgetName }}</div>
            </template>
            <template v-if="column.dataIndex === 'amount' || column.dataIndex === 'usableAmount'">
              {{ formatMoney(record[column.dataIndex]) }}
            </template>
          </template>
        </a-table>
      </div>

      <!-- 底部按钮区域 -->
      <div class="flex justify-center mt-6">
        <div class="flex gap-4">
          <a-button
            class="w-32"
            @click="modalVisible = false"
          >关闭</a-button>
          <a-button
            class="w-32"
            type="primary"
            @click="handleConfirm"
            :disabled="!selectedBudget"
          >确定</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { frontTip } from '@/common/js/pub-methods.js';
import { useTable } from '@/composables/useTableHook.js';
import { formatMoney } from "@/utils/tools.js"
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'select', 'cancel']);

// 弹窗可见性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 搜索表单数据
const searchForm = reactive({
  budgetName: '',
  year: '',
  fundNature: ''
});

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    fixed: 'left',
    customRender: ({ index }) => index + 1
  },
  {
    title: '项目名称',
    dataIndex: 'budgetName',
    width: 260,
    align: 'center'
  },
  {
    title: '预算年度',
    dataIndex: 'year',
    width: 100,
    align: 'center'
  },
  {
    title: '资金性质',
    dataIndex: 'fundNature',
    width: 160,
    align: 'center'
  },
  {
    title: '指标类型',
    dataIndex: 'indexType',
    width: 100,
    align: 'center'
  },
  {
    title: '业务主管处室',
    dataIndex: 'department',
    width: 120,
    align: 'center'
  },
  {
    title: '指标总金额(元)',
    dataIndex: 'amount',
    width: 140,
    align: 'center'
  },
  {
    title: '已使用金额(元)',
    dataIndex: 'usedAmount',
    width: 140,
    align: 'center'
  },
  {
    title: '可用金额(元)',
    dataIndex: 'usableAmount',
    width: 140,
    align: 'center'
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    width: 100,
    align: 'center',
    fixed: 'right'
  },
];

// 选中的预算
const selectedBudget = ref(null);
const selectedRowKeys = ref([]);
// 模拟获取预算列表数据
const fetchBudgetList = async (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          budgetName: 'XXXXXXXX项目',
          year: '2025',
          fundNature: '一般公共预算资金',
          indexType: 'XXXX',
          department: 'XXXX',
          amount: 10002.01,
          usedAmount: 290000.00,
          usableAmount: 290000.00,
          usageCount: 1
        },
        {
          id: '2',
          budgetName: 'XXXXXXXX项目',
          year: '2025',
          fundNature: '一般公共预算资金',
          indexType: 'XXXX',
          department: 'XXXX',
          amount: 10002.01,
          usedAmount: 10002.01,
          usableAmount: 290000.00,
          usageCount: 0
        },
        {
          id: '3',
          budgetName: 'XXXXXXXX项目',
          year: '2025',
          fundNature: '一般公共预算资金',
          indexType: 'XXXX',
          department: 'XXXX',
          amount: 10002.01,
          usedAmount: 10002.01,
          usableAmount: 290000.00,
          usageCount: 0
        },
        {
          id: '4',
          budgetName: 'XXXXXXXX项目',
          year: '2025',
          fundNature: '一般公共预算资金',
          indexType: 'XXXX',
          department: 'XXXX',
          amount: 10002.01,
          usedAmount: 10002.01,
          usableAmount: 290000.00,
          usageCount: 0
        },
        {
          id: '5',
          budgetName: 'XXXXXXXX项目',
          year: '2025',
          fundNature: '一般公共预算资金',
          indexType: 'XXXX',
          department: 'XXXX',
          amount: 10002.01,
          usedAmount: 10002.01,
          usableAmount: 290000.00,
          usageCount: 0
        }
      ];

      // 筛选数据
      let filteredData = [...mockData];
      if (params.budgetName) {
        filteredData = filteredData.filter(item =>
          item.budgetName.includes(params.budgetName)
        );
      }
      if (params.year) {
        filteredData = filteredData.filter(item =>
          item.year === params.year
        );
      }
      if (params.fundNature) {
        filteredData = filteredData.filter(item =>
          item.fundNature === params.fundNature
        );
      }

      resolve({
        rows: filteredData,
        total: 101
      });
    }, 500);
  });
};

// 使用useTable钩子管理表格数据
const {
  loading,
  list,
  pagination,
  setFilterOption,
  reset,
  handleTableChange,
  refresh
} = useTable(fetchBudgetList, {
  autoLoad: false,
  initialFilter: searchForm,
  initialPage: 1,
  initialPageSize: 5,
  onSuccess: (res, rows) => {
    // 清空选中状态
    selectedBudget.value = null;
    selectedRowKeys.value = [];
  }
});

// 监听弹窗打开
watch(() => modalVisible.value, (val) => {
  if (val) {
    // 初始化数据
    handleReset();
    refresh();
  }
});

// 处理重置
const handleReset = () => {
  searchForm.budgetName = '';
  searchForm.year = '';
  searchForm.fundNature = '';
  selectedBudget.value = null;
  selectedRowKeys.value = [];
  reset();
};

// 处理行选择变化
const onSelectChange = (selectedKeys, selectedRows) => {
  selectedRowKeys.value = selectedKeys;
  if (selectedRows && selectedRows.length > 0) {
    selectedBudget.value = selectedRows[0];
  } else {
    selectedBudget.value = null;
  }
};
// 单选行选择
const handleCustomRow = (row) => {
  return {
    onClick: () => {
      const key = row.id;
      // 单选逻辑：点击行时选中当前行
      onSelectChange([key], [row]);
    },
  };
}

// 确认选择
const handleConfirm = () => {
  if (!selectedBudget.value) {
    frontTip('warning', '请选择一个预算指标');
    return;
  }
  emit('select', selectedBudget.value);
  modalVisible.value = false;
};
</script>

<style lang="scss">
.budget-index-selector-modal {
  @include custom-modal;
}
</style>