import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import legacy from '@vitejs/plugin-legacy';
import { AntDesignVueResolver } from "unplugin-vue-components/resolvers"
import path from 'path'
export default ({ command, mode }) => {
  const env = loadEnv(mode, process.cwd());
  return defineConfig({
    base: env.VITE_BASE_PATH,
    plugins: [
      //1.非组件函数，指令自动导入
      AutoImport({
        //可以自动导入vue相关api 其他模块函数
        imports: ['vue', 'vue-router', 'pinia'],
      }),
      //2.组件自动导入
      Components({
        resolvers: [
          AntDesignVueResolver({
            importStyle: false,
            resolveIcons: true
          }),],
      }),
      vue(),
      //低版本浏览器兼容，
      legacy({
        targets: ['defaults', 'not IE 11'],
        polyfills: true,
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'], //异步函数/生成器
        renderLegacyChunks: true, // 是否渲染旧版代码块
      })
    ],
    // 解析处理额外的scss文件
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          silenceDeprecations: ['legacy-js-api'],
          additionalData: `@use "@/assets/styles/index.scss" as *;`
        }
      },
    },
    // 去除生产环境 日志
    esbuild: {
      drop: ['console', 'debugger'],
    },
    // 打包优化
    build: {
      minify: "esbuild",
      // 禁用 gzip 压缩大小报告，可略微减少打包时间
      reportCompressedSize: false,
      // 规定触发警告的 chunk 大小
      chunkSizeWarningLimit: 1500,
      // rollupOptions: {
      //   // 配置rollup输出选项
      //   output: {
      //     //静态资源分类打包
      //     chunkFileNames: `assets/js/[name]-[hash].js`, //代码块文件名
      //     entryFileNames: `assets/js/[name]-[hash].js`, //入口文件名
      //     assetFileNames: `assets/[ext]/[name]-[hash].[ext]`, // 资源文件名
      //     manualChunks (id) {
      //       if (id.includes('node_modules')) {
      //         //使用pnpm打包 .split('node_modules/')后下标取2 如果不是pnpm 下标就取1
      //         return id.toString().split('node_modules/')[2].split('/')[0].toString()
      //       }
      //     },
      //   },
      // },
    },
    // 设置代理服务器
    server: {
      cors: true,
      proxy: {
        '/proxy': {
          target: env.VITE_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/proxy/, ''),
        }
      }
    },
    // 设置 '@' 别名指向 src 目录
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    // 本地启动之前显示的优化预构建依赖
    optimizeDeps: {
      include: [
        'ant-design-vue/es',
        'ant-design-vue/es/button',
        'ant-design-vue/es/tabs',
        'ant-design-vue/es/table',
        'ant-design-vue/es/pagination',
        'ant-design-vue/es/form',
        'ant-design-vue/es/input',
        'ant-design-vue/es/card',
        'ant-design-vue/es/timeline',
        'ant-design-vue/es/select',
        'ant-design-vue/es/drawer',
        'ant-design-vue/es/modal',
        'ant-design-vue/es/message',
        'ant-design-vue/es/tree',
        '@antv/x6'
      ],
    },
    // 定义esm- builder构建版本下的一些特性 为了更好的tree-shaking（减少生产环境下的包体积）和ES模块的支持
    define: {
      // 在生产环境中启用或禁用 Vue Devtools 支持
      '__VUE_PROD_DEVTOOLS__': false,
      // 在生产环境中启用或禁用详细的 hydration mismatch 报告。
      '__VUE_PROD_HYDRATION_MISMATCH_DETAILS__': false,
    }
  })
}

