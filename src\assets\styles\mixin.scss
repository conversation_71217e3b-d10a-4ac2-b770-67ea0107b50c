// 混合和函数
// --------------------------------
@use './common.scss'as *;
@use './theme.scss'as *;

// 基础图标
@mixin base-icon($size, $p) {
  display: inline-block;
  width: $size;
  height: $size;
  content: url($icon-url+$p);
  vertical-align: middle;
}

// 触发动效
@mixin active-jump {
  transform: scale(1.02);
}

// 背景
@mixin bg($p) {
  background: url($bg-url+$p) no-repeat;
  background-size: 100% 100%;
  position: relative;
  z-index: 0;
}


// 菜单主页
@mixin menu-wrapper {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 16px;
}

//antd表格个性化重置 高度自适应：内容区占满表格全部（除分页区）高度 
@mixin custom-table {
  .ant-spin-nested-loading {
    height: 100%;

    .ant-spin-container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .ant-table {
      overflow: hidden;
      flex: 1;

      .ant-table-container {
        height: 100%;
        display: flex;
        flex-direction: column;


        .ant-table-header {
          flex-shrink: 0;
          /* 确保表头不被压缩 */
          overflow-x: hidden !important;
          /* 防止表头出现横向滚动条 */
          border-bottom: 1px solid #f0f0f0;
        }

        .ant-table-body {
          height: 100%;
        }

        //控制表头不换行 影响高度计算
        .ant-table-thead>tr>th {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }

    .ant-table-pagination.ant-pagination {
      border-top: 1px solid #f0f0f0;
      margin-top: 0;
      padding: 12px 8px 0 12px;
    }
  }
}

@mixin custom-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    background-color: $primary-color;
    border-radius: 4px 4px 0 0;

    .ant-modal-title {
      color: white;
      font-weight: 500;
    }
  }

  .ant-modal-content {
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .ant-modal-close {
    color: white;

    &:hover {
      color: rgba(255, 255, 255, 0.85);
    }
  }

}

@mixin custom-details-page {
  .white-space {
    height: 8px;
    background-color: $primary-bg-color;
  }

  :deep(.ant-page-header) {
    padding: 8px 20px;

    .page-header-title {
      font-weight: normal;
      font-size: 18px;
      font-family: $font-title;

    }
  }


  :deep(.ant-card) {
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03), 0 0 2px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .ant-card-head {
      min-height: auto;
      padding: 0 16px;
      border-bottom: 1px solid #f0f0f0;
      // background-color: #fafafa;
      border-radius: 6px 6px 0 0;

      .ant-card-head-title {
        padding: 10px 0;
      }
    }

    .ant-card-body {
      padding: 16px;
    }
  }

  :deep(.ant-tabs) {

    .ant-tabs-nav {
      margin-bottom: 1px;

      .ant-tabs-nav-wrap {
        .ant-tabs-nav-list {
          .ant-tabs-tab {
            padding: 8px 0;
          }
        }
      }
    }
  }

  :deep(.ant-descriptions-item-label) {
    width: 160px;
    padding: 12px 16px;
    background-color: #f5f8fa;
  }
}

//全屏弹窗
@mixin full-screen-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
    overflow-y: auto;
  }

  .ant-modal-body {
    flex: 1;
  }
}


// 清除浮动
@mixin clearfix() {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// 文本截断（单行省略）
@mixin text-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本截断
@mixin multi-line-ellipsis($lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

// 滚动条美化
@mixin scrollbar($width: 6px, $thumb-color: rgba(0, 0, 0, 0.3), $track-color: transparent) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }

  &::-webkit-scrollbar-thumb {
    background-color: $thumb-color;
    border-radius: $width / 2;
  }

  &::-webkit-scrollbar-track {
    background-color: $track-color;
  }
}

// 绝对定位居中
@mixin absolute-center() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}



// 卡片样式
@mixin card($padding: $card-padding, $radius: $border-radius-base, $shadow: $shadow-card) {
  background-color: $bg-color-light;
  border-radius: $radius;
  box-shadow: $shadow;
  padding: $padding;
}



// 渐变背景
@mixin gradient-bg($start-color, $end-color, $direction: to right) {
  background: $start-color;
  background: linear-gradient($direction, $start-color, $end-color);
}

// 文本样式
@mixin text-style($color: $text-primary-color, $font-size: $font-size-base, $font-weight: $font-weight-normal, $line-height: $line-height-base) {
  color: $color;
  font-size: $font-size;
  font-weight: $font-weight;
  line-height: $line-height;
}

// 标题样式
@mixin heading-style($size: $h4-font-size, $color: $text-primary-color, $margin-bottom: $spacing-md, $font-weight: $font-weight-semibold) {
  font-size: $size;
  color: $color;
  margin-bottom: $margin-bottom;
  font-weight: $font-weight;
  line-height: $line-height-heading;
  font-family: $font-family-heading;
}