import { createRouter, createWebHistory } from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { constantRoutes } from './routes';
import { setupRouterGuard } from './guard';

/**
 * 进度条配置
 */
NProgress.configure({
  showSpinner: false,
  easing: 'ease',
  speed: 500,
  minimum: 0.2
});

/**
 * 创建路由实例
 * 基础路由直接注册
 */
const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_PATH || ''),
  routes: constantRoutes,
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// 设置路由守卫
setupRouterGuard(router);

export default router;