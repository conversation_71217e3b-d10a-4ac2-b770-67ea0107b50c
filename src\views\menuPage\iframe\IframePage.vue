<template>
  <div>
    <!-- 我是模板页面  报表或者普通的html页面-->
    <iframe
      :src="iframeUrl"
      frameborder="0"
      width="100%"
      height="90%"
    ></iframe>
  </div>
</template>

<script setup>
import { sessionCache } from "@/utils/cache.js";
const sid = sessionCache.get('tk_sid') || '';
const route = useRoute();
const iframeUrl = computed(() => (route.meta.iframeUrl.startsWith('.') ? route.meta.iframeUrl.slice(1) : route.meta.iframeUrl) + '?sid=' + sid);
onMounted(() => {
  console.log('IframePageUrl', useRoute().meta.iframeUrl, iframeUrl.value);
});
</script>

<style lang="scss" scoped></style>