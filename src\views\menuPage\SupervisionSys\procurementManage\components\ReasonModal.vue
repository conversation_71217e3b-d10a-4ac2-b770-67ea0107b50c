<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="modalTitle"
    :maskClosable="false"
    :width="500"
    centered
    class="reason-modal"
  >
    <template #icon>
      <component :is="modalIcon" />
    </template>
    <div class="px-2 animate__animated animate__fadeIn animate_faster">
      <div class="flex items-start mb-4">
        <div class="flex-shrink-0 mr-3">
          <div
            class="flex items-center justify-center w-10 h-10 rounded-full shadow-sm"
            :class="type === 'cancel' ? 'bg-red-50' : 'bg-blue-50'"
          >
            <ExclamationCircleOutlined
              v-if="type === 'cancel'"
              class="text-xl text-red-500"
            />
            <InfoCircleOutlined
              v-else
              class="text-xl text-blue-500"
            />
          </div>
        </div>
        <div class="flex-1 animate__animated animate__fadeInRight animate_faster">
          <h3
            class="text-base font-medium"
            :class="type === 'cancel' ? 'text-red-500' : 'text-blue-500'"
          >
            {{ type === 'cancel' ? '您正在作废以下采购项目' : '您正在变更以下采购项目' }}
          </h3>
          <p class="mt-1 text-gray-600 break-all">
            {{ projectName }}
          </p>
        </div>
      </div>

      <a-form
        layout="vertical"
        ref="formRef"
        class="animate__animated animate__fadeIn animate_faster"
      >
        <a-alert
          class="mb-4"
          :message="type === 'cancel' ? '注意：可作废未提交中台备案的项目' : '注意：项目采购可变更未被需求使用的项目'"
          :type="type === 'cancel'
            ? 'warning'
            : 'info'"
        />
        <a-form-item
          :label="type === 'cancel'
            ? '作废原因'
            : '变更原因'"
          required
        >
          <a-textarea
            v-model:value="reason"
            :placeholder="`请详细描述${type === 'cancel' ? '作废' : '变更'}原因（必填）`"
            :rows="4"
            :maxlength="200"
            show-count
            allow-clear
            :class="type === 'cancel'
              ? 'border-red-300 focus:border-red-500 hover:border-red-400 transition-all duration-300'
              : 'border-blue-300 focus:border-blue-500 hover:border-blue-400 transition-all duration-300'"
          />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <div class="flex justify-end space-x-2">
        <a-button
          @click="handleCancel"
          :disabled="loading"
        >
          取消
        </a-button>
        <a-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :class="type === 'cancel' ? 'bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600' : ''"
        >
          确认
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { frontTip } from "@/common/js/pub-methods.js";
import { h } from 'vue';
import {
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'cancel', // 'cancel' 或 'change'
  },
  projectName: {
    type: String,
    default: ''
  },
  projectId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

// 内部弹窗状态
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单引用
const formRef = ref(null);
// 原因值
const reason = ref('');
// 加载中状态
const loading = ref(false);

// 计算属性获取标题
const modalTitle = computed(() => {
  return props.type === 'cancel' ? '采购项目作废确认' : '采购项目变更确认';
});

// 获取图标
const modalIcon = computed(() => {
  return props.type === 'cancel'
    ? h(ExclamationCircleOutlined, { style: 'color: #ff4d4f' })
    : h(InfoCircleOutlined, { style: 'color: #1890ff' });
});

// 取消操作
const handleCancel = () => {
  reason.value = '';
  modalVisible.value = false;
};

// 提交操作
const handleSubmit = () => {
  // 表单验证
  if (!reason.value.trim()) {
    frontTip('warning', `请输入${props.type === 'cancel' ? '作废' : '变更'}原因`);
    return;
  }

  // 设置加载状态
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    emit('confirm', {
      id: props.projectId,
      reason: reason.value,
      type: props.type
    });

    // 重置状态
    loading.value = false;
    reason.value = '';
    modalVisible.value = false;
  }, 1000);
};

// 监听弹窗打开时重置状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    reason.value = '';
    loading.value = false;
  }
});
</script>

<style lang="scss" scoped></style>