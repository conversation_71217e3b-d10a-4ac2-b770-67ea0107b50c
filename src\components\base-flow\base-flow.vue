<template>
  <a-modal
    v-model:visible="modalVisible"
    title="流程图"
    :width="1200"
    :footer="null"
    :mask-closable="false"
  >
    <div class="h-[560px]">
      <div
        id="flow-container"
        ref="container"
      ></div>
    </div>
  </a-modal>

</template>

<script setup>
import { Graph } from '@antv/x6'
import { nodeConfigs } from './default-nodeconfig.js';
import jsonData from './data.js';
const container = ref(null);
const loading = ref(false);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:visible']);
const modalVisible = computed({
  get () {
    return props.visible;
  },
  set (value) {
    emit('update:visible', value);
  },
});
//注册流程图自定义节点
// const data = jsonData;
const registerCustomNodes = () => {
  // 遍历节点配置，注册所有自定义节点
  Object.entries(nodeConfigs).forEach(([shape, config]) => {
    // 为文本添加通用的定位属性
    const textAttrs = {
      ...config.attrs.text,
      refX: 0.5,
      refY: 0.5,
      textAnchor: 'middle',
      textVerticalAnchor: 'middle',
    };

    // 注册节点
    Graph.registerNode(
      shape,
      {
        inherit: config.inherit,
        width: config.width,
        height: config.height,
        attrs: {
          ...config.attrs,
          text: textAttrs,
        },
        ports: config.ports,
      },
      true
    );
  });
}
const mockData = async () => {
  loading.value = true;
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(jsonData)
      loading.value = false;
    }, 2000)
  })
}
const initGraph = () => {
  registerCustomNodes();
  const graph = new Graph({
    container: container.value,
    autoResize: true, //自适应画布大小
    background: {
      color: '#f5f7fa',
    },

    panning: true,
    mousewheel: true,
    grid: {
      visible: true,
      type: 'dot',
      size: 10,
      color: '#e0e0e0',
    },
    connecting: {
      router: 'manhattan', // 路由类型：直角折线 router自动推断哪些点需要拐弯
      connector: {
        name: 'rounded',   // 拐弯处连线样式：圆角
        args: {
          radius: 8,       // 圆角半径
        },
      },
      anchor: 'center',         // 锚点位置
      connectionPoint: 'boundary', // 连接点类型
      allowBlank: false,        // 不允许连接到空白处
      snap: {
        radius: 20,             // 吸附半径
      }
    }
  })
  graph.fromJSON(jsonData);
  graph.resize(container.offsetWidth, container.offsetHeight)
}
onMounted(async () => {

})
watchEffect(() => {
  // 监听容器尺寸
  if (container.value) {
    initGraph()
  }
})
</script>

<style lang="scss" scoped></style>