.ant-tag {
  font-size: 13px;
  line-height: 24px;
  padding: 0 8px;
}

/* 表单禁用字体 */
.ant-input[disabled],
.ant-select-disabled .ant-select-selection-item,
.ant-checkbox-disabled+span,
.ant-radio-disabled+span,
.ant-input-number-disabled .ant-input-number-input,
.ant-picker-disabled .ant-picker-input>input,
.ant-cascader-picker-disabled .ant-cascader-input,
.ant-textarea-disabled,
.ant-mentions-disabled>textarea {
  color: rgba(0, 0, 0, 0.6) !important;
}

/* 后缀按钮不可用隐藏 */
.ant-input[disabled]+.ant-input-group-addon-disabled {
  display: none;
}

.ant-input {
  resize: none;
}

.ant-btn.ant-btn-primary {
  border: none;
}

/* 全局antd-vue样式 */
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  font-weight: normal;
  font-family: 'AlibabaPuHuiTi-bold';
  background-color: #196fee;
  color: #fff;
}

.ant-empty-normal {
  margin: 16px 0;
}



.ant-table_border--bt .ant-table {
  border-bottom: 1px solid #f0f0f0;
}

/* 表头表身样式 */
.ant-table .ant-table-thead>tr>th {
  font-weight: 500;
  padding: 8px 12px !important;
  background-color: #f5f8fa;
  /* font-family: $font-title; */
  font-size: inherit;
  color: #606266;
}

.ant-table .ant-table-tbody>tr>td {
  color: rgba(1, 1, 1, 0.7);
  font-size: inherit;
  padding: 10px 12px;
  font-size: 13.5px;
}


.ant-table-tbody>tr>td {
  padding: 12px 8px;
}

/* tab更多 */
.ant-tabs>.ant-tabs-nav .ant-tabs-nav-more {
  padding: 4px 16px;
}

.ant-tabs .ant-tabs-tab-btn {
  font-size: 16px;
}

/* 弹窗tab */
.ant-modal .ant-tabs .ant-tabs-tab-btn {
  font-size: 15px;
}

.ant-modal .ant-tabs-tab+.ant-tabs-tab {
  margin: 0 0 0 24px;
}

/* 卡片 */
.ant-card .ant-card-body {
  padding: 16px;
}

/* description描述列表 */
.ant-modal .ant-descriptions .ant-descriptions-row>td+td {
  padding-left: 12px;
}

.ant-descriptions-row>td {
  padding-bottom: 12px;
}

.ant-descriptions-bordered .ant-descriptions-item-label {
  width: 160px;
}


/* 弹窗step */
.ant-modal .modal-step-display .ant-steps-item-wait .ant-steps-item-title {
  max-width: 180px;
  white-space: normal;
  line-height: 24px;
  color: #196fee;
  font-family: 'AlibabaPuHuiTi-bold'
}

/* icon */
.ant-modal .modal-step-display .ant-steps-item-wait .ant-steps-item-icon {
  background-color: #196fee;
  border-color: #196fee;
}

.ant-modal .modal-step-display .ant-steps-item-wait .ant-steps-item-icon .ant-steps-icon {
  color: #fff;
}


.ant-modal .modal-step-display .ant-steps-item-wait .ant-steps-item-title::after {
  background-color: #196fee;
  height: 2px;
}

/* 描述 */
.ant-modal .modal-step-display .ant-steps-item-wait .ant-steps-item-description {
  max-width: 180px;
  margin-top: 4px;
  font-size: 14px;
  color: #727171;
}

/* 悬浮 */

.ant-modal .modal-step-display .ant-steps-item:not(.ant-steps-item-active)>.ant-steps-item-container:hover .ant-steps-item-icon .ant-steps-icon {
  color: #fff !important;
}


.ant-modal .modal-step-display .ant-steps-item:not(.ant-steps-item-active)>.ant-steps-item-container:hover .ant-steps-item-description {
  color: rgba(0, 0, 0, 0.7);
}

/* 弹窗内业务步骤条样式 */
.ant-modal .modal-custom-step:not(.ant-steps-label-vertical) .ant-steps-item-description {
  max-width: 140px;
}

.ant-modal .modal-custom-step .ant-steps-item .ant-steps-item-title {
  font-family: 'AlibabaPuHuiTi-bold'
}

.ant-modal .modal-custom-step .ant-steps-item .ant-steps-item-title::after {
  height: 1.5px;
}

.ant-modal .modal-custom-step .ant-steps-item-finish>.ant-steps-item-container>.ant-steps-item-icon {
  background-color: #196fee;
  border: none;
}

.ant-modal .modal-custom-step .ant-steps-item-finish>.ant-steps-item-container>.ant-steps-item-icon .ant-steps-icon {
  color: #fff;
}

.ant-modal .modal-custom-step .ant-steps-item-finish>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title,
.ant-modal .modal-custom-step .ant-steps-item-process>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title {
  color: #196fee;

}

.ant-modal .modal-custom-step .ant-steps-item-process>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title::after {
  background-color: #196fee;
}

.ant-modal .modal-custom-step .ant-steps-item-process>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-description,
.ant-modal .modal-custom-step .ant-steps-item-finish>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-description {
  color: #767676;
}

.ant-modal .modal-custom-step .ant-steps-item-wait>.ant-steps-item-container>.ant-steps-item-icon {
  background-color: #b0b8c3;
  border: none;

}

.ant-modal .modal-custom-step .ant-steps-item-wait>.ant-steps-item-container>.ant-steps-item-icon .ant-steps-icon {
  font-size: 14px;
  color: #fff;
}

.ant-modal .modal-custom-step .ant-steps-item-wait>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title::after {
  background-color: #ceced0;
  height: 1.5px;
}

.ant-modal .modal-custom-step .ant-steps-item-finish>.ant-steps-item-container:hover .ant-steps-item-icon .ant-steps-icon {
  color: #fff !important;
}

.ant-modal .modal-custom-step .ant-modal .anticon-exclamation-circle svg {
  width: 24px;
  height: 24px;
}



/* 自定义抽屉 标题与关闭按钮位置 */
.ant-drawer .ant-drawer-header-title {
  flex-direction: row-reverse;
}

.ant-drawer.guide-drawer .ant-steps-vertical>.ant-steps-item {
  margin-bottom: 4px;
}


/* 标题 */
.ant-drawer.guide-drawer .ant-steps-vertical>.ant-steps-item .ant-steps-item-content .ant-steps-item-title {
  color: #196fee;
  font-family: 'AlibabaPuHuiTi-bold';
  line-height: 24px;
  font-size: 15px;
}

/* 描述 */
.ant-drawer.guide-drawer .ant-steps-vertical>.ant-steps-item .ant-steps-item-content .ant-steps-item-description {
  color: #333;
  font-size: 13px;
}

/* 悬浮 */
.ant-drawer.guide-drawer .ant-steps-vertical>.ant-steps-item:not(.ant-steps-item-active)>.ant-steps-item-container:hover .ant-steps-item-description {
  color: #333;
}

.ant-tree .ant-tree-node-pcontent-wrapper.ant-tree-node-selected {
  background-color: rgba(38, 157, 232, .1);
}



/* 自定义卡片 */
.custom-card.ant-card .ant-card-head {
  padding: 0 12px;
  background-color: #fafafa;
}

.custom-card.ant-card .ant-card-head .ant-card-head-title {
  padding: 12px;
}

.custom-card.ant-card .ant-card-head .ant-card-extra {
  padding: 0;
}

.custom-card.ant-card+.ant-card {
  margin-left: 10px;
}

/* card内有表格的时候 */
.custom-card.list .ant-card-body {
  padding: 1px 0;
}



/* 表单弹窗 */
/* 标签 */
.form-dialog .form-item__inner .ant-form-item-label {
  width: 150px;
}

/* 表单 */
.form-dialog .form-item__inner .ant-form-item-control .ant-form-item-control-input {
  width: 200px;
}

/* 标签 */
.form-dialog.lg .form-item__inner .ant-form-item-label {
  width: 220px;
}

/* 表单 */
.form-dialog.lg .form-item__inner .ant-form-item-control .ant-form-item-control-input {
  width: 220px;
}