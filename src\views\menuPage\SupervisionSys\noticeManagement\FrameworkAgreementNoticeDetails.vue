<template>
  <div class="notice-details-page">
    <div
      class="flex flex-col h-full"
      ref="pageRef"
    >
      <a-page-header
        :ghost="false"
        @back="handleBack"
      >
        <template #title>
          <span class="page-header-title">采购征集公告详情</span>
        </template>
      </a-page-header>
      <div
        class="white-space"
        style="height: 6px;"
      ></div>

      <div class="overflow-y-auto flex-1overflow-x-hidden">
        <div class="p-6 bg-white rounded-md shadow-sm">
          <div class="mb-8 text-center">

            <h1 class="text-xl text-gray-800"><a-tag color="warning">{{ noticeData.area }}</a-tag>{{
              noticeData.title }}</h1>
            <div class="mt-3 text-gray-500">发布时间：{{ noticeData.publishTime }}</div>
          </div>

          <section-title title="一、征集项目概述" />
          <div class="content-section">
            <info-item
              label="征集项目名称"
              :value="noticeData.projectName"
            />
            <info-item
              label="征集项目编号"
              :value="noticeData.projectCode"
            />
          </div>

          <section-title title="二、征集人信息" />
          <div class="content-section">
            <info-item
              label="征集人名称"
              :value="noticeData.purchaserName"
            />
            <info-item
              label="联系人"
              :value="noticeData.contactPerson"
            />
            <info-item
              label="联系方式"
              :value="noticeData.contactWay"
            />
            <info-item
              label="联系地址"
              :value="noticeData.address"
            />
          </div>

          <section-title title="三、入围供应商及产品（服务）信息" />
          <div class="content-section">
            <info-item
              label="包号"
              :value="`${noticeData.packageNo}，分包名称：${noticeData.packageName}`"
            />
            <info-item
              label="(1) 最高入围价格/最高入围分"
              :is-title="true"
            />
            <info-item
              label="桌面操作系统"
              :value="`${noticeData.maxPrice}元`"
            />
            <info-item
              label="(2) 评审小组成员名单"
              :value="noticeData.reviewers"
              :is-title="true"
            />
            <info-item
              label="(3) 入围供应商报价内容"
              :is-title="true"
            />

            <div class="mt-4 overflow-x-auto">
              <table class="data-table">
                <thead>
                  <tr>
                    <th
                      v-for="header in tableHeaders"
                      :key="header"
                    >{{ header }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(item, index) in noticeData.supplierList"
                    :key="index"
                  >
                    <td>{{ item.order }}</td>
                    <td class="text-left">{{ item.supplierName }}</td>
                    <td class="text-left">{{ item.address }}</td>
                    <td>{{ item.productName }}</td>
                    <td>{{ item.brand }}</td>
                    <td>{{ item.model }}</td>
                    <td>{{ item.price }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 路由相关
const router = useRouter();
const route = useRoute();

// 返回上一页
const handleBack = () => {
  router.back();
};

// 表格头部
const tableHeaders = [
  '排序',
  '入围供应商',
  '地址',
  '入围产品名称',
  '品牌',
  '规格型号',
  '入围价格'
];

// 公告数据
const noticeData = reactive({
  area: '省本级',
  title: '',
  publishTime: '2025-01-10 15:39:25',
  projectName: '湖南省各级预算单位2024年操作系统、数据库框架协议采购项目',
  projectCode: 'K43000000020240000003',
  purchaserName: '湖南省公共资源交易中心',
  contactPerson: '业务二部',
  contactWay: '0731-89665152、0731-89665170',
  address: '湖南省长沙市雨花区万家丽南路二段29号',
  packageNo: '1',
  packageName: '包1',
  maxPrice: '390',
  reviewers: '徐代道、余诗权、赵青、游德交、祝秋宇',
  supplierList: [
    {
      order: '1',
      supplierName: '统信软件技术有限公司',
      address: '北京市北京经济技术开发区科创一街10号院12号楼18层',
      productName: '桌面操作系统',
      brand: '统信/UOS',
      model: 'V20',
      price: '390元'
    }
  ]
});

// 标题组件
const SectionTitle = (props) => {
  return h('h2', {
    class: 'text-base pl-3 mb-2 mt-5',
    style: {
      fontFamily: 'AlibabaPuHuiTi-bold'
    }
  }, props.title);
};

// 信息项组件
const InfoItem = (props) => {
  const isTitle = props.isTitle || false;

  return h('p', {
    class: 'mb-2'
  }, [
    h('span', {
      class: isTitle ? 'font-medium block mb-2' : 'font-medium'
    }, `${props.label}${isTitle ? '' : '：'}`),
    !isTitle && h('span', null, props.value)
  ]);
};

onMounted(() => {
  // 获取详情数据
  const { id } = route.params;
  const { prjName } = route.query;
  console.log('公告ID:', id);
  console.log('项目名称:', prjName);
  noticeData.projectName = prjName;
  noticeData.title = prjName + '入围成交结果公告';
  // 这里应该调用实际的详情API获取数据
  // const fetchNoticeDetails = async () => {
  //   try {
  //     const res = await getNoticeDetails(id);
  //     if (res.code === '0000') {
  //       Object.assign(noticeData, res.data);
  //     }
  //   } catch (error) {
  //     console.error('获取详情失败:', error);
  //   }
  // };
  // fetchNoticeDetails();
});
</script>

<style lang="scss" scoped>
.notice-details-page {
  height: 100%;
  @include custom-details-page;
}



.content-section {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.data-table {
  width: 100%;
  border-collapse: collapse;

  th,
  td {
    border: 1px solid #e2e8f0;
    padding: 0.75rem 1rem;
    text-align: center;
  }

  th {
    background-color: #f7fafc;
  }

  td.text-left {
    text-align: left;
  }

  tr:hover {
    background-color: #f8fafc;
  }
}
</style>