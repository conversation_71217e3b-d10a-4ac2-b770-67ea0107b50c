<script setup>
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { useLoadingStore } from '@/stores/useLoadingStore';
import { ConfigProvider } from 'ant-design-vue';
// ant-design-vue第三个版本 dayjs模块用于格式化，所以也需要另外配置中文化 
import 'dayjs/locale/zh-cn';
import dayjs from 'dayjs'
import { onMounted } from 'vue';
dayjs.locale('zh-cn');
// 预订制antd主题样式
const themeColor = {
  main: '#456df8',
  danger: 'red',
}
// 可以通过其他操作改变
const changeTheme = (color) => {
  ConfigProvider.config({
    theme: {
      primaryColor: color,
    }
  });
}
// 获取加载状态
const loadingStore = useLoadingStore();
const loading = computed(() => loadingStore.loading);
const loadingText = computed(() => loadingStore.loadingText);
onMounted(() => {
  changeTheme(themeColor.main);
})
</script>

<template>
  <a-config-provider :locale="zhCN">
    <router-view></router-view>
    <Loading
      :visible="loading"
      :text="loadingText"
    />
  </a-config-provider>
</template>

<style lang="scss"></style>
