<template>
  <div class="h-full detail-page">
    <component
      v-if="showComponent"
      :is="showComponent"
      :id="projectId"
    />
    <div
      v-else
      class="flex items-center justify-center h-full"
    >
      <div class="text-2xl text-gray-500">暂无详情页</div>
    </div>
  </div>
</template>

<script setup>
import FrameworkProcurement1 from './detailsPage/FrameworkProcurement1.vue';
import FrameworkProcurement2 from './detailsPage/FrameworkProcurement2.vue';
import { PURCHASE_METHOD_OPTIONS } from '@/common/js/global.js';
//详情中转站
const route = useRoute();
// 通过 query 传参，缓存也有判断
const entranceType = ref(route.query?.entrance || '');
const projectId = route.params.id;

// 根据采购方式判断使用哪个组件
const showComponent = computed(() => {
  // 使用对象映射替代多个if-else条件判断
  const componentMap = {
    [PURCHASE_METHOD_OPTIONS[1].value]: FrameworkProcurement1, // 框架协议一阶段
    [PURCHASE_METHOD_OPTIONS[2].value]: FrameworkProcurement2, // 框架协议二阶段
  };

  // 直接从映射中获取组件名称
  return componentMap[entranceType.value] || '';
});
</script>

<style lang="scss" scoped>
// 通用详情页样式
.project-details-page {
  height: 100%;
  @include custom-details-page;
}
</style>