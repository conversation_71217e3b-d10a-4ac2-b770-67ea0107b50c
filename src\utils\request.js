// 二次封装axios 
import axios from 'axios'
import { getResStatusMsg } from "@/common/js/pub-methods.js"
import { sessionCache } from "@/utils/cache.js"
import { frontTip } from '@/common/js/pub-methods.js'
// import { sm2EncryptUuid, sm4Encrypt, sm2SignVerify, sm4Decrypt } from "@/utils/sm-crypto.js"
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_API_PATH,
  // 超时
  timeout: 60000
})

// request拦截器
service.interceptors.request.use(config => {
  const tk_sid = sessionCache.get('tk_sid');
  // console.log('请求参数', config.data)
  // 1.token携带 
  if (tk_sid) {
    config.params = config.params ? config.params : {};
    config.params.sid = tk_sid;
  }
  // // 2.参数加密处理=> post请求   表单二进制数据上传不需要加密
  // if (config.method === 'post' && config.headers['Content-Type'] !== 'multipart/form-data') {
  //   let uuid = sm2EncryptUuid();
  //   let cipher_text = sm4Encrypt(config.data);
  //   config.data = {
  //     uuid,
  //     cipher_text
  //   }
  // }
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})
// 响应拦截器
const BASE_URL = import.meta.env.VITE_BASE_PATH;
const NO_AUTH_CODES = ['888', '000', '0', '999'];
service.interceptors.response.use(response => {
  // 1.用户身份信息校验
  if (response.status == 222) {
    frontTip("error", "登录失效，请重新登录！")
    window.location.replace(`${BASE_URL}401`);
    return
  }
  //2.无权限访问页面校验
  let forbidCode = response.data.result_code || response.data.code;
  if (forbidCode && NO_AUTH_CODES.includes(forbidCode)) {
    return getResStatusMsg(403);
  }
  return Promise.resolve(response.data)
  // return handleDecryptResData(response.data) || Promise.resolve(response.data);
}, err => {
  // 超时处理
  if (axios.isAxiosError(err)) {
    if (err.code === 'ECONNABORTED' && err.message.includes('timeout')) {
      // 处理超时错误
      frontTip("error", "请求超时，请稍后再试！");
      window.location.replace(`${BASE_URL}error`);
      return Promise.reject(new Error('请求超时'));
    }
  }
  // 根据状态码进行处理反馈
  if (err && err.response) {
    let status = err.response.status;
    // 状态码判断
    getResStatusMsg(status);
  } else {
    console.error("请求错误", err)
    return Promise.reject(err)
  }
})
// 解密
const handleDecryptResData = (resData) => {
  const { uuid, uuid_sign, cipher_text } = resData;
  if (uuid && uuid_sign && cipher_text) {
    if (sm2SignVerify(uuid, uuid_sign)) {
      const res = sm4Decrypt(cipher_text, uuid);
      return res.data || res
    }
  }
}
export default service