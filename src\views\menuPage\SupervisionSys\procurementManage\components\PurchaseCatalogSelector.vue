<template>
  <a-modal
    v-model:visible="modalVisible"
    title="选择采购目录"
    :width="800"
    :footer="null"
    :mask-closable="false"
    wrap-class-name="purchase-catalog-selector-modal"
  >
    <div class="px-2">
      <!-- 顶部区域 -->
      <div class="flex items-center justify-between p-3 mb-4 border border-blue-100 rounded-md">
        <div class="flex items-center gap-4">
          <a-checkbox
            v-model:checked="filterOptions.centralPurchase"
            @change="handleFilterChange"
            class="text-blue-700"
          >
            <span class="font-medium">政府集中采购</span>
          </a-checkbox>
          <a-checkbox
            v-model:checked="filterOptions.decentralizedPurchase"
            @change="handleFilterChange"
            class="text-blue-700"
          >
            <span class="font-medium">分散采购</span>
          </a-checkbox>
        </div>
        <div class="w-1/2">
          <a-input-search
            v-model:value="searchValue"
            placeholder="请输入关键词搜索"
            @search="onSearchClick"
            allow-clear
            class="search-input"
            enter-button
          >
            <template #enterButton>
              <a-button
                type="primary"
                class="flex items-center justify-center"
              >
                <search-outlined />
              </a-button>
            </template>
          </a-input-search>
        </div>
      </div>

      <!-- 树形选择区域 -->
      <div class="border border-gray-200 rounded-md shadow-sm">
        <div class="p-2 pl-4 text-gray-600 border-b border-gray-200 plfont-medium bg-gray-50">
          采购目录列表
        </div>
        <a-spin :spinning="loading">
          <div
            class="p-3 pl-4"
            style="height: 350px; overflow-y: auto;"
          >
            <a-tree
              v-model:expandedKeys="expandedKeys"
              v-model:checkedKeys="checkedKeys"
              :tree-data="filteredTreeData"
              :field-names="{ children: 'children', title: 'name', key: 'value' }"
              @check="onCheck"
              :selectable="false"
              :checkable="true"
              :show-line="true"
              :show-icon="false"
              :blockNode="true"
              :checkStrictly="true"
            >
              <template #title="{ key, name, data }">
                <div class="flex items-center py-1 cursor-pointer tree-node-content">
                  <span :class="{
                    'text-blue-700 font-medium': checkedKeys.includes(key),
                    'text-blue-500': highlightedKeys.includes(key) && !checkedKeys.includes(key),
                    'text-gray-700': !checkedKeys.includes(key) && !highlightedKeys.includes(key)
                  }">
                    {{ key }} {{ name }}
                  </span>
                  <a-tag
                    v-if="data && data.purchaseType"
                    :class="{
                      'ml-4 text-xs text-blue-500': checkedKeys.includes(key),
                      'ml-4 text-xs text-gray-500': !checkedKeys.includes(key)
                    }"
                  >
                    {{ data.purchaseType }}
                  </a-tag>
                </div>
              </template>
            </a-tree>
          </div>
        </a-spin>
      </div>
      <div class="flex justify-between p-3 mt-4 border border-gray-200 rounded bg-gray-50">
        <div class="flex items-center">
          <span class="mr-2 font-medium text-gray-600">已选择：</span>
          <div
            v-if="selectedItem"
            class="flex-1 text-blue-600"
          >
            {{ selectedItem.key + '-' + selectedItem.name }}
            <span class="ml-2 text-xs text-gray-500">{{ selectedItem.purchaseType }}</span>
          </div>
          <div
            v-else
            class="flex-1 text-gray-400"
          >请选择一个采购目录项</div>
        </div>
        <a-button @click="handleReset">
          <template #icon>
            <redo-outlined />
          </template>
          重置
        </a-button>
      </div>

      <div class="flex justify-center mt-6">
        <div class="flex gap-4">
          <a-button
            class="w-32"
            @click="modalVisible = false"
          >取消</a-button>
          <a-button
            class="w-32"
            type="primary"
            :disabled="!selectedItem"
            @click="handleConfirm"
          >确定</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { debounce, cloneDeep } from 'lodash-es';
import { SearchOutlined, RedoOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  treeData: {
    type: Array,
    default: () => ([])
  },
  // selectList: {
  //   type: Array,
  //   default: () => ([])
  // }
});

const emit = defineEmits(['update:visible', 'select']);

// 弹窗可见性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});


// 筛选选项
const filterOptions = reactive({
  centralPurchase: true,
  decentralizedPurchase: true
});

// 搜索相关
const searchValue = ref('');
const loading = ref(false);
const expandedKeys = ref([]);
const checkedKeys = ref([]);
const highlightedKeys = ref([]);

// 当前选中的项目
const selectedItem = ref(null);

// 采购类型常量
const PURCHASE_TYPE = {
  CENTRAL: '政府集中采购',
  DECENTRALIZED: '分散采购'
};
// 通用树遍历方法
const traverseTree = (nodes, options, path = []) => {
  if (!nodes) return false;

  const { callback } = options;

  for (const node of nodes) {
    // 执行回调，如果回调返回true则终止遍历
    if (callback(node, path)) return true;

    // 如果有子节点且数量大于0，递归遍历
    if (node.children && node.children.length > 0) {
      // 将当前节点添加到路径
      const currentPath = [...path, node.value];

      // 递归遍历子节点
      if (traverseTree(node.children, options, currentPath)) {
        return true; // 传递终止信号
      }
    }
  }

  return false;
};
// 根据筛选条件过滤树数据
const filteredTreeData = computed(() => {
  // if (!filterOptions.centralPurchase && !filterOptions.decentralizedPurchase) {
  //   return [];
  // }

  // 深拷贝树数据以避免修改原始数据
  const processedData = cloneDeep(props.treeData);

  // 使用通用的树遍历方法处理树节点
  const processTree = (nodes) => {
    if (!nodes || !nodes.length) return [];

    return nodes.map(node => {
      // 创建节点副本
      const newNode = { ...node };

      // 如果是叶子节点，根据筛选条件判断是否保留
      if (newNode.isLeaf) {
        // 如果不符合筛选条件，返回null表示过滤掉
        if (
          (newNode.purchaseType === PURCHASE_TYPE.CENTRAL && filterOptions.centralPurchase) ||
          (newNode.purchaseType === PURCHASE_TYPE.DECENTRALIZED && filterOptions.decentralizedPurchase)
        ) {
          // 符合条件的叶子节点保留 
          return newNode;
        }
        return null
      }

      // 处理非叶子节点
      if (newNode.children && newNode.children.length) {
        // 递归处理子节点
        const filteredChildren = processTree(newNode.children);

        // 如果过滤后没有子节点，则该节点也应被过滤掉
        if (filteredChildren.length === 0) {
          return null;
        }

        // 保留有子节点的节点，并更新其children
        newNode.children = filteredChildren;
        return newNode;
      }

      // 没有子节点的非叶子节点，默认过滤掉
      return null;
    }).filter(Boolean); // 过滤掉null节点
  };

  return processTree(processedData);
});

// 搜索点击处理
const onSearchClick = (value) => {
  // 执行搜索
  if (value) {
    searchTree(value);
  } else {
    loading.value = true;
    setTimeout(() => {
      // 如果搜索值为空，刷新操作只展开一级目录
      console.log('xxx', filteredTreeData.value);
      expandedKeys.value = filteredTreeData.value.map(rootNode => rootNode.key);
      highlightedKeys.value = [];
      loading.value = false;
    }, 300);
  }
};

// 搜索树
const searchTree = (value) => {
  loading.value = true;

  // 模拟异步搜索
  setTimeout(() => {
    const keys = [];
    const expandKeys = [];

    // 使用通用树遍历函数
    traverseTree(props.treeData, {
      callback: (node, path) => {
        // 检查节点标题或值是否包含搜索关键词
        if (
          node.name.toLowerCase().includes(value.toLowerCase()) ||
          node.key.toLowerCase().includes(value.toLowerCase())
        ) {
          keys.push(node.key);
          expandKeys.push(...path, node.key);
        }
      }
    });

    // 更新高亮和展开的键
    highlightedKeys.value = [...new Set(keys)];
    expandedKeys.value = [...new Set(expandKeys)];
    loading.value = false;
  }, 300);
};

// 筛选条件变化
const handleFilterChange = () => {
  // 重置搜索
  searchValue.value = '';
  highlightedKeys.value = [];
};


// 处理复选框选择 - 修改为单选逻辑
const onCheck = (checkedKeysValue, info) => {
  // 确保只能选择一个节点
  if (info.checked) {
    console.log(checkedKeysValue, info.node, '---复选框');
    // 如果选中了一个节点，取消其他所有节点的选中状态
    const key = info.node.key;
    // 设置为单选
    checkedKeys.value = [key];
    // console.log(checkedKeysValue, 'checkedKeys.value');
    // 查找并设置选中的节点
    updateSelectedItem(key);
  } else {
    // 如果取消选中，清空选中状态
    selectedItem.value = null;
    checkedKeys.value = [];
  }
};



// 查找并更新选中的节点
const updateSelectedItem = (key) => {
  let foundNode = null;

  // 使用通用树遍历函数查找节点
  traverseTree(props.treeData, {
    callback: (node) => {
      if (node.key === key) {
        foundNode = node;
        return true; // 找到节点，终止遍历
      }
    }
  });

  // 设置选中的节点
  selectedItem.value = foundNode;
};
const handleReset = () => {
  selectedItem.value = null;
  checkedKeys.value = [];
  onSearchClick();
}
// 确认选择
const handleConfirm = () => {
  if (!selectedItem.value) {
    return;
  }
  console.log('selectedItem:', selectedItem.value);
  // 将选中项信息抛出(暂时抛出title和value)
  emit('select', { key: selectedItem.value?.key, title: selectedItem.value?.name });

  modalVisible.value = false;

};
// 处理选中节点的禁用状态
const updateDisabledState = (selectedCodes) => {
  const setStatusConfig = (nodes, state) => {
    // 如果没有选中的节点，则所有节点都可选
    traverseTree(nodes, {
      callback: (node) => {
        node.disabled = state;
      }
    });
  }
  if (!selectedCodes || selectedCodes.length === 0) {
    setStatusConfig(props.treeData, false);
    return;
  }

  // 先将所有节点设置为可选
  setStatusConfig(props.treeData, false);

  // 对每个选中的节点，禁用其子节点
  selectedCodes.forEach(code => {
    let foundNode = null;
    // 查找选中的节点
    traverseTree(props.treeData, {
      callback: (node) => {
        if (node.key === code) {
          foundNode = node;
          return true; // 找到节点，终止当前分支遍历
        }
      }
    });

    if (foundNode && foundNode.children && foundNode.children.length > 0) {
      // 禁用该节点的所有子节点
      setStatusConfig(foundNode.children, true)
    }
  });
};
watch(() => props.visible, (newVal) => {
  if (newVal && selectedItem.value === null) {
    // 初次展开所有一级节点
    onSearchClick();
  }
})
// watch(() => props.selectList, (newVal) => {
//   console.log(newVal, '所选值');
//   let codes = newVal.map(i => {
//     if (i.includes('-')) {
//       return i.split('-')[0] || '';
//     }
//   })
//   updateDisabledState(codes)
// }, { deep: true, immediate: true })

// //每次拿到选到的code列表，然后递归遍历找到该节点，并将该节点的所有子节点增加 disabled为true(不含该节点), 其他节点则设置为false


</script>

<style lang="scss">
.purchase-catalog-selector-modal {
  @include custom-modal;

  .search-input {
    .ant-input {
      border-radius: 4px;
    }

    .ant-input-search-button {
      background-color: $primary-color;
      border-color: $primary-color;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;

      .anticon {
        color: white;
      }
    }
  }

  .ant-tree {
    .ant-tree-treenode {
      padding: 2px 0;
      margin-bottom: 2px;
      display: flex;
      align-items: center;


      .tree-node-content {
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s;
        width: 100%;
      }

      // 悬浮效果 - 浅灰色背景
      &:hover .tree-node-content {
        background-color: #f5f7fa;
      }

      // 选中效果 - 浅蓝色背景
      &.ant-tree-treenode-selected .tree-node-content {
        background-color: #e6f0fa;
      }
    }

    .ant-tree-node-content-wrapper {
      flex: 1;
      padding: 0;
      transition: all 0.2s;
      display: flex;
      align-items: center;

      // 移除默认的选中和悬浮背景
      &.ant-tree-node-selected,
      &:hover {
        background-color: transparent !important;
      }
    }

    .ant-tree-switcher {
      color: $primary-color;
    }

    .ant-tree-indent-unit {
      width: 18px;
    }

    // 树节点连线样式
    &.ant-tree-show-line {
      .ant-tree-indent-unit::before {
        border-color: #d1d5db;
      }

      .ant-tree-switcher-line-icon {
        color: $primary-color;
        vertical-align: middle;
        transform: translateY(2px);
      }
    }

    // 复选框样式
    .ant-tree-checkbox {
      margin: 0 4px 0 2px;
      vertical-align: middle;
      top: -.5px;

      .ant-tree-checkbox-inner {
        border-color: #94a3b8;
        border-radius: 2px;
        width: 16px;
        height: 16px;
      }

      &.ant-tree-checkbox-checked .ant-tree-checkbox-inner {
        background-color: $primary-color;
        border-color: $primary-color;
      }

      &:hover .ant-tree-checkbox-inner {
        border-color: $primary-color;
      }
    }
  }

  // 按钮样式
  .ant-btn {
    &:disabled {
      background-color: #bfdbfe;
      border-color: #bfdbfe;
      color: #fff;
    }

  }

}
</style>
