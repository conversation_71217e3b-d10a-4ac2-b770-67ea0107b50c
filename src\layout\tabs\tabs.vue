<template>
  <div :class="['tab-container', { 'tab-container-shadow': isScrolled }]">
    <a-tabs
      v-model:activeKey="activeTabKey"
      hide-add
      type="editable-card"
      @edit="onTabRemove"
      size="small"
      @change="onTabChange"
      :tabBarGutter="4"
    >
      <template #rightExtra>
        <a-space style="transform: translateY(-4px);">
          <i
            class="icon-clean"
            @click="cleanTabs"
          ></i>
          <i
            class="icon-fullscreen"
            @click="toggleFullScreen"
          ></i>
        </a-space>
      </template>

      <a-tab-pane
        v-for="(pane, idx) in tabsList"
        :key="pane.menuCode"
        :tab="pane.menuName"
        :closable="idx === 0 ? false : true"
      >
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { useTabsStore } from '@/stores/useTabsStore';
import toggleFullScreen from '@/utils/fullscreen';

import navigateService from '@/utils/navigate';

const props = defineProps({
  isScrolled: {
    type: Boolean,
    default: false
  }
});

const tabsStore = useTabsStore();

// 标签页列表和当前激活的标签
const tabsList = computed(() => tabsStore.getTabs);
const activeTabKey = computed({
  get: () => tabsStore.getActiveTab,
  set: (value) => tabsStore.setActiveTab(value)
});

// 处理标签点击
const onTabChange = (key) => {
  if (!key) return;
  // 导航到对应菜单
  navigateService.navigateByMenuCode(key);
};

// 处理关闭标签
const onTabRemove = (targetKey) => {
  removeTab(targetKey);
};

// 移除标签
const removeTab = (targetKey) => {
  tabsStore.removeTab(targetKey);
  // 获取当前激活的标签
  const activeKey = tabsStore.getActiveTab;
  if (activeKey) {
    navigateService.navigateByMenuCode(activeKey);
  } else {
    // 如果没有标签了，回到工作台
    navigateService.navigateToWorkplace();
  }
};

// 清除所有标签（除了首页）
const cleanTabs = () => {
  tabsStore.clearTabs();
  navigateService.navigateToWorkplace();
};



</script>

<style lang="scss" scoped>
.tab-container {
  height: $tabs-bar-height;
  background-color: $bg-color-tabs;
  padding: 4.5px 16px 0 10px;
  position: relative;
  // overflow: visible;
  /* 确保弧形边框可见 */
  transition: box-shadow 0.3s ease;
  z-index: 10;

  &.tab-container-shadow {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  :deep(.ant-tabs) {
    .ant-tabs-tab {
      position: relative;
      background-color: $bg-color-tab;
      border: 1px solid $border-color-tab;
      border-bottom: none;
      border-radius: 6px 6px 0 0;
      margin-right: 2px;
      transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;

      // 为所有标签准备伪元素，但默认不显示
      &::before,
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        width: 10px;
        height: 10px;
        background-color: transparent;
        border-radius: 0;
        z-index: 1;
        opacity: 0; // 默认不显示
        transition: opacity 0.2s ease-in-out;
      }

      &::before {
        left: -10px;
        border-bottom-right-radius: 10px;
      }

      &::after {
        right: -10px;
        border-bottom-left-radius: 10px;
      }

      .ant-tabs-tab-btn {
        font-weight: 500;
        font-size: 12px;
        color: $text-color-tab;
        position: relative;
        z-index: 2;

        &:hover {
          color: $primary-color;
        }
      }

      .ant-tabs-tab-remove {
        position: relative;
        z-index: 2;

        svg {
          display: block;
          width: 10px;
          height: 10px;
          opacity: 0.6;
          color: $bg-color-tab-remove-icon;

          &:hover {
            color: $hover-color-tab-remove-icon;
            opacity: 1;
          }
        }
      }
    }

    /* 激活tab */
    .ant-tabs-tab-active {
      background: $primary-bg-color;

      // 只为激活的标签显示弧形效果
      &::before {
        opacity: 1; // 显示伪元素
        box-shadow: 5px 5px 0 5px $primary-bg-color;
        border-right: 1px solid $border-color-tab;
        border-bottom: 1px solid $border-color-tab;
      }

      &::after {
        opacity: 1; // 显示伪元素
        box-shadow: -5px 5px 0 5px $primary-bg-color;
        border-left: 1px solid $border-color-tab;
        border-bottom: 1px solid $border-color-tab;
      }

      .ant-tabs-tab-btn {
        color: $primary-color;
      }
    }
  }

  :deep(.ant-tabs-top>.ant-tabs-nav) {
    margin: 0;
  }

  // 标签页过多icon 调整边距
  :deep(.ant-tabs>.ant-tabs-nav .ant-tabs-nav-more, .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more) {
    padding: 2px 16px;
  }

  .icon-fullscreen {
    transition: all .3s;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
    }
  }

  .icon-clean {
    transition: all .3s;
    cursor: pointer;

    &:hover {
      transform: rotateZ(-20deg);
    }

    &:active {
      transform: scale(1.1);
      transform: rotateZ(0);
    }
  }
}

.icon-clean {
  @include base-icon(16px, 'icon-clean.svg');
}

.icon-fullscreen {
  @include base-icon(16px, 'icon-fullscreen.svg');
}
</style>