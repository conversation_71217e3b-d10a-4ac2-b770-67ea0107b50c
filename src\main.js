import { createApp } from 'vue';
import App from './App.vue';
import { createPinia } from "pinia";
import { NavigatePlugin } from './utils/navigate';
import router from '@/router/index.js';
//按需导入echarts的配置项
// import '@/utils/echarts.js'
// import Echarts from "vue-echarts";
// antd的全局样式
// import 'ant-design-vue/dist/antd.min.css';
import 'nprogress/nprogress.css';
// 导入可变样式
import 'ant-design-vue/dist/antd.variable.min.css';
// 全局css
import '@/common/css/style.css';
import '@/common/css/animate.css';
const app = createApp(App);
const pinia = createPinia();
// 注册全局组件
// app.component('v-chart', Echarts);
// 全局设置页面最小高度
app.provide("ROOT_MIN_HEIGHT", 640);
app.use(pinia);
app.use(router);
app.use(NavigatePlugin);
app.mount("#app");
