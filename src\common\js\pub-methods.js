import { message, Modal, Upload } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined, InfoCircleOutlined, CheckCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

message.config({
  duration: 2,
  maxCount: 3,
});
// form表单中的一些校验规则
export const handlePhoneValidator = async (_rule, value) => {
  if (value === "" || value.trim() === "") {
    return Promise.reject('请输入手机号');
  } else if (!/^1[3456789]\d{9}$/.test(value)) {
    return Promise.reject('请输入正确的手机号');
  } else {
    return Promise.resolve();
  }
}
// 登录密码强度校验
export const handlePwdValidator = async (_rule, value) => {
  if (value === "" || value.trim() === "") {
    return Promise.reject('请输入密码');
    // 密码校验必须大小写字母加数字组成并且不少于6位
  } else if (!/^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{6,}$/.test(value)) {
    return Promise.reject('密码必须由字母数字特殊字符组成，并且不少于6位');
  } else {
    return Promise.resolve();
  }
}
//身份证号校验
export const handleIdNumberValidator = async (_rule, value) => {
  if (value === "" || value.trim() === "") {
    return Promise.reject('请输入身份证号码');
  } else if (!/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
    return Promise.reject('请输入正确的身份证号码');
  } else {
    return Promise.resolve();
  }
}
// 短信验证码校验
export const handleSmsValidator = async (_rule, value) => {
  if (value.trim().length < 4 || value.trim().length > 6) {
    return Promise.reject('请输入有效的验证码');
  } else {
    return Promise.resolve();
  }
}

/**
 * 错误日志处理函数。根据日志类型显示不同类型的提示信息，并在控制台输出对应日志。
 * @param {('success'|'warn'|'info'|'error')} logType - 日志类型，决定显示的提示类型。
 * @param {string} userMessage - 前端显示的提示信息。
 * @param {string} logMsg - 控制台输出的详细日志信息，默认与前端提示信息相同。
 */
export const frontTip = (logType, userMessage = '返回数据异常', logMsg = userMessage) => {
  // 使用TypeScript的类型守卫来确保logType的有效性
  if (logType !== 'success' && logType !== 'warn' && logType !== 'info' && logType !== 'error') {
    console.error('Invalid log type provided.');
    return;
  }
  // 根据日志类型调用相应的方法，并统一使用log函数减少代码重复
  switch (logType) {
    case 'success':
      message.success(userMessage);
      console.log(`output-logMsg`, logMsg)
      break;
    case 'warn':
      message.warn(userMessage);
      console.warn(`output-logMsg`, logMsg)
      break;
    case 'error':
      message.error(userMessage);
      console.error(`output-logMsg`, logMsg)
      break;
    default: // 'error'
      message.info(userMessage);
      console.info(`output-logMsg`, logMsg)
      break;
  }
};

/**
 * 根据状态码获取对应的提示信息并进行处理。
 * @param {number} status HTTP状态码。
 */
// 定义一个函数，根据传入的状态码返回相应的提示信息
export const getResStatusMsg = (status) => {
  // 判断传入的状态码是否为数字且不为空
  // 判断传入的状态码是否为数字且不为空
  if (typeof status !== "number" || !status) {
    console.error("status无效！无法返回提示信息！")
    return;
  }


  // 定义重定向函数
  const redirectPath = (targetPath, tip) => {
    frontTip("error", tip)
    window.location.replace(`${import.meta.env.VITE_BASE_PATH}/${targetPath}`);
  }

  // 定义一个对象，存储不同状态码对应的处理函数
  switch (status) {
    case 401:
      return redirectPath(401, '登录失效，请重新登录！'); // 特殊处理登录失效的情况
    case 403:
      return redirectPath(403, '权限不足，无法访问!');
    case 404:
      return frontTip("error", "请求接口地址不存在！");
    case 500:
      return frontTip("error", "服务器错误,请稍后！")
    case 502:
      return frontTip("error", "网关错误！")
    default:
      return frontTip("error", "网络错误！"); // 或者其他默认处理逻辑
  }

}



/**
 * 上传限制:投诉相关pdf
 * @param {string} file 要上传的文件
 */
export const beforeUpload = file => {
  // 上传文件必须为PDF格式，切不超过50m
  const isPDF = file.type === 'application/pdf';
  const isLt50M = file.size / 1024 / 1024 < 50;
  // console.log(isPDF, isLt50M)
  if (!isPDF) {
    frontTip('warn', '文件当前只支持PDF格式');
    return Upload.LIST_IGNORE; //Upload.LIST_IGNORE; =>不符合要求时阻止它进入列表
  }

  if (!isLt50M) {
    frontTip('warn', '文件当前只支持50M以内');
    return Upload.LIST_IGNORE;
  }
  return isPDF && isLt50M
}
/**
 * 二次确认弹窗封装（支持多类型）
 * @param {string} type 弹窗类型（save/delete/warn/info），默认info
 * @param {string} title 标题
 * @param {string} content 内容
 * @param {function} okcb 确认回调

 */
export const popConfirm = (
  okcb = () => { },
  type = 'info',
  title = '',
  content = '',
) => {
  // 类型映射表
  const typeMap = {
    save: {
      icon: createVNode(CheckCircleOutlined, { style: 'color: #52c41a' }),
      okType: 'primary',
      defaultTitle: '保存确认',
      defaultContent: '请确认，是否要保存当前内容？',
    },
    delete: {
      icon: createVNode(ExclamationCircleOutlined, { style: 'color: #ff4d4f' }),
      okType: 'danger',
      defaultTitle: '删除确认',
      defaultContent: '此操作不可恢复，是否确认删除？',
    },
    warn: {
      icon: createVNode(ExclamationCircleOutlined, { style: 'color: #faad14' }),
      okType: 'primary',
      defaultTitle: '警告',
      defaultContent: '请注意，该操作可能会带来风险。',
    },
    info: {
      icon: createVNode(InfoCircleOutlined, { style: 'color: #1890ff' }),
      okType: 'primary',
      defaultTitle: '提示',
      defaultContent: '请确认，是否执行该操作。',
    },
  };
  const config = typeMap[type] || typeMap['info'];
  Modal.confirm({
    title: title || config.defaultTitle,
    content: content || config.defaultContent,
    okText: '确认',
    okType: config.okType,
    cancelText: '取消',
    onOk: okcb,
    icon: config.icon,
  });
}

export const formatDate = (obj, format = 'YYYY-MM-DD') => {
  return obj ? dayjs(obj).format(format) : "";
};


