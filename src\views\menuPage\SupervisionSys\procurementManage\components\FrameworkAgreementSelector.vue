<template>
  <a-modal
    v-model:visible="modalVisible"
    title="选择一阶段项目"
    :width="1200"
    :footer="null"
    :mask-closable="false"
    style="top:60px"
    wrap-class-name="framework-agreement-selector-modal"
  >
    <div class="p-4">
      <!-- 搜索区域 -->
      <div class="mb-4">
        <div class="grid grid-cols-3 gap-4 mb-4">
          <div class="flex items-center">
            <span class="mr-2 text-right whitespace-nowrap w-28">交易项目名称：</span>
            <a-input
              v-model:value="searchForm.frameworkName"
              placeholder="请输入"
              allow-clear
              class="flex-1"
            />
          </div>
          <div class="flex items-center">
            <span class="mr-2 text-right whitespace-nowrap w-28">交易项目编码：</span>
            <a-input
              v-model:value="searchForm.frameworkCode"
              placeholder="请输入"
              allow-clear
              class="flex-1"
            />
          </div>
          <div class="flex items-center">
            <span class="mr-2 text-right whitespace-nowrap w-28">征集人名称：</span>
            <a-input
              v-model:value="searchForm.solicitantName"
              placeholder="请输入"
              allow-clear
              class="flex-1"
            />
          </div>
        </div>
        <div class="grid grid-cols-3 gap-4 mb-4">
          <div class="flex items-center">
            <span class="mr-2 text-right whitespace-nowrap w-28">采购类型：</span>
            <a-select
              v-model:value="searchForm.purchaseType"
              placeholder="请选择"
              allow-clear
              class="flex-1"
            >
              <a-select-option value="货物">货物</a-select-option>
              <a-select-option value="工程">工程</a-select-option>
              <a-select-option value="服务">服务</a-select-option>
            </a-select>
          </div>
          <div class="flex items-center">
            <span class="mr-2 text-right whitespace-nowrap w-28">组织形式：</span>
            <a-select
              v-model:value="searchForm.orgType"
              placeholder="请选择"
              allow-clear
              class="flex-1"
            >
              <a-select-option value="集中采购">集中采购</a-select-option>
              <a-select-option value="分散采购">分散采购</a-select-option>
            </a-select>
          </div>
          <div class="flex items-center">
            <span class="mr-2 text-right whitespace-nowrap w-28">采购实施形式：</span>
            <a-select
              v-model:value="searchForm.implType"
              placeholder="请选择"
              allow-clear
              class="flex-1"
            >
              <a-select-option value="委托协议采购">委托协议采购</a-select-option>
              <a-select-option value="自行采购">自行采购</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="flex justify-end">
          <a-space :size="12">
            <a-button
              type="primary"
              @click="setFilterOption(searchForm)"
            >查询</a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="bg-white rounded">
        <a-table
          :dataSource="list"
          :columns="columns"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange"
          :scroll="{ x: 1200, y: 320 }"
          row-key="id"
          :custom-row="handleCustomRow"
          :row-selection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'frameworkName'">
              <div class="break-all whitespace-normal">{{ record.frameworkName }}</div>
            </template>
            <template v-if="column.dataIndex === 'applicableScope'">
              <div class="break-all whitespace-normal">{{ record.applicableScope }}</div>
            </template>
            <template v-if="column.dataIndex === 'budget'">
              {{ formatMoney(record.budget) }}
            </template>
          </template>
        </a-table>
      </div>

      <!-- 底部按钮区域 -->
      <div class="flex justify-center mt-6">
        <div class="flex gap-4">
          <a-button
            class="w-32"
            @click="modalVisible = false"
          >关闭</a-button>
          <a-button
            class="w-32"
            type="primary"
            @click="handleConfirm"
          >确定</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { frontTip } from '@/common/js/pub-methods.js';
import { useTable } from '@/composables/useTableHook.js';
import { formatMoney } from "@/utils/tools.js";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'select']);

// 弹窗可见性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 搜索表单数据
const searchForm = reactive({
  frameworkName: '',
  frameworkCode: '',
  solicitantName: '',
  purchaseType: undefined,
  orgType: undefined,
  implType: undefined
});

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    customRender: ({ index }) => index + 1
  },
  {
    title: '交易项目名称',
    dataIndex: 'frameworkName',
    width: 280,
    align: 'center'
  },
  {
    title: '交易项目编码',
    dataIndex: 'frameworkCode',
    width: 150,
    align: 'center'
  },
  {
    title: '项目类型',
    dataIndex: 'projectType',
    width: 150,
    align: 'center'
  },
  {
    title: '采购类型',
    dataIndex: 'purchaseType',
    width: 100,
    align: 'center'
  },
  {
    title: '组织形式',
    dataIndex: 'orgType',
    width: 100,
    align: 'center'
  },
  {
    title: '采购方式',
    dataIndex: 'purchaseMethod',
    width: 100,
    align: 'center'
  },
  {
    title: '采购实施形式',
    dataIndex: 'implType',
    width: 120,
    align: 'center'
  },
  {
    title: '所属年度',
    dataIndex: 'year',
    width: 100,
    align: 'center'
  },
  {
    title: '财政区划',
    dataIndex: 'fiscalRegion',
    width: 100,
    align: 'center'
  },
  {
    title: '预算金额',
    dataIndex: 'budget',
    width: 120,
    align: 'center'
  },
  {
    title: '征集机构',
    dataIndex: 'solicitantName',
    width: 150,
    align: 'center',
    fixed: 'right'
  }
];

// 选中的框架协议
const selectedAgreement = ref(null);
const selectedRowKeys = ref([]);

// 模拟获取框架协议列表数据
const fetchAgreementList = async (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          frameworkCode: 'XXXXXXXX',
          frameworkName: '湖南省公共资源交易中心湖南省各级预算单位2024年便携式计算机框架协议采购项目',
          projectType: '协议框架协议项目',
          purchaseType: '货物',
          orgType: '集中采购',
          purchaseMethod: '委托协议采购',
          implType: '委托协议采购',
          year: '2025',
          fiscalRegion: '永州市',
          budget: 10000.00,
          solicitantName: '湖南省公共资源交易中心'
        },
        {
          id: '2',
          frameworkCode: 'XXXXXXXX',
          frameworkName: '湖南省公共资源交易中心湖南省各级预算单位2024年办公用品框架协议采购项目',
          projectType: '协议框架协议项目',
          purchaseType: '货物',
          orgType: '集中采购',
          purchaseMethod: '委托协议采购',
          implType: '委托协议采购',
          year: '2025',
          fiscalRegion: '长沙市',
          budget: 5000.00,
          solicitantName: '湖南省公共资源交易中心'
        },
        {
          id: '3',
          frameworkCode: 'XXXXXXXX',
          frameworkName: '湖南省公共资源交易中心湖南省各级预算单位2024年网络设备框架协议采购项目',
          projectType: '协议框架协议项目',
          purchaseType: '货物',
          orgType: '集中采购',
          purchaseMethod: '委托协议采购',
          implType: '委托协议采购',
          year: '2025',
          fiscalRegion: '株洲市',
          budget: 8000.00,
          solicitantName: '湖南省公共资源交易中心'
        },
        {
          id: '4',
          frameworkCode: 'XXXXXXXX',
          frameworkName: '湖南省公共资源交易中心湖南省各级预算单位2024年软件产品框架协议采购项目',
          projectType: '协议框架协议项目',
          purchaseType: '货物',
          orgType: '集中采购',
          purchaseMethod: '委托协议采购',
          implType: '委托协议采购',
          year: '2025',
          fiscalRegion: '娄底市',
          budget: 6000.00,
          solicitantName: '湖南省公共资源交易中心'
        },
        {
          id: '5',
          frameworkCode: 'XXXXXXXX',
          frameworkName: '湖南省公共资源交易中心湖南省各级预算单位2024年数据服务框架协议采购项目',
          projectType: '协议框架协议项目',
          purchaseType: '服务',
          orgType: '集中采购',
          purchaseMethod: '委托协议采购',
          implType: '委托协议采购',
          year: '2025',
          fiscalRegion: '岳阳市',
          budget: 12000.00,
          solicitantName: '湖南省公共资源交易中心'
        }
      ];

      // 筛选数据
      let filteredData = [...mockData];
      if (params.frameworkName) {
        filteredData = filteredData.filter(item =>
          item.frameworkName.includes(params.frameworkName)
        );
      }
      if (params.frameworkCode) {
        filteredData = filteredData.filter(item =>
          item.frameworkCode.includes(params.frameworkCode)
        );
      }
      if (params.solicitantName) {
        filteredData = filteredData.filter(item =>
          item.solicitantName.includes(params.solicitantName)
        );
      }
      if (params.purchaseType) {
        filteredData = filteredData.filter(item =>
          item.purchaseType === params.purchaseType
        );
      }
      if (params.orgType) {
        filteredData = filteredData.filter(item =>
          item.orgType === params.orgType
        );
      }
      if (params.implType) {
        filteredData = filteredData.filter(item =>
          item.implType === params.implType
        );
      }

      resolve({
        rows: filteredData,
        total: 101
      });
    }, 500);
  });
};

// 使用useTable hook管理表格数据
const {
  loading,
  list,
  pagination,
  setFilterOption,
  reset,
  handleTableChange,
  refresh
} = useTable(fetchAgreementList, {
  autoLoad: false,
  initialFilter: searchForm,
  initialPage: 1,
  initialPageSize: 5,
  onSuccess: (res, rows) => {
    // 清空选中状态
    selectedAgreement.value = null;
    selectedRowKeys.value = [];
  }
});

// 监听弹窗打开
watch(() => modalVisible.value, (val) => {
  if (val) {
    // 初始化数据
    handleReset();
  }
});

// 处理重置
const handleReset = () => {
  searchForm.frameworkName = '';
  searchForm.frameworkCode = '';
  searchForm.solicitantName = '';
  searchForm.purchaseType = undefined;
  searchForm.orgType = undefined;
  searchForm.implType = undefined;
  selectedAgreement.value = null;
  selectedRowKeys.value = [];
  reset();
};

// 处理行选择变化
const onSelectChange = (selectedKeys, selectedRows) => {
  selectedRowKeys.value = selectedKeys;
  if (selectedRows && selectedRows.length > 0) {
    selectedAgreement.value = selectedRows[0];
  } else {
    selectedAgreement.value = null;
  }
};

// 单选行选择
const handleCustomRow = (row) => {
  return {
    onClick: () => {
      const key = row.id;
      // 单选逻辑：点击行时选中当前行
      onSelectChange([key], [row]);
    },
  };
}

// 确认选择
const handleConfirm = () => {
  if (!selectedAgreement.value) {
    frontTip('info', '请选择一个框架协议');
    return;
  }
  emit('select', selectedAgreement.value);
};
</script>

<style lang="scss">
.framework-agreement-selector-modal {
  @include custom-modal;
}
</style>