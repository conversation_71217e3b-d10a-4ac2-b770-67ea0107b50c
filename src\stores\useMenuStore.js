import { defineStore } from 'pinia';
import { sessionCache } from "@/utils/cache.js"
import { DEFAULT_MENU_ITEM } from "@/common/js/global.js"
const Layout = () => import('@/layout/index.vue')
// 批量导入形成映射表
const pageModules = import.meta.glob('../views/menuPage/**/*.vue');

export const useMenuStore = defineStore('menu', () => {
  // 菜单状态配置
  const menuState = reactive({
    collapsed: false,
    expandedKeys: [],
  });
  const breadcrumbs = ref([]);

  // 动态路由是否已添加
  const isDynamicAddedRoute = ref(false);

  // 当前用户菜单 :默认菜单
  const menuList = ref([
    DEFAULT_MENU_ITEM
  ]);
  // 菜单页面控件权限
  const permissionList = ref([]);
  // 计算属性
  const getMenuCollapsed = computed(() => menuState.collapsed);
  const getBreadcrumbs = computed(() => breadcrumbs.value);
  const getExpandedKeys = computed(() => menuState.expandedKeys);
  const getIsDynamicAddedRoute = computed(() => isDynamicAddedRoute.value);
  const getMenuList = computed(() => menuList.value);

  // 缓存
  const initState = () => {
    const cache = sessionCache.getJSON('breadcrumbs');
    if (cache) {
      breadcrumbs.value = cache;
    }
    setMenuCollapsed(false);
  }
  // 设置方法
  const setMenuCollapsed = (value) => {
    menuState.collapsed = value;
  };

  const setExpandedKeys = (value) => {
    menuState.expandedKeys = value;
  };


  const setBreadcrumbs = (value) => {
    if (value) {
      let temp = value.map(i => {
        return { name: i.title || i.name, path: i.path || '/' };
      });
      breadcrumbs.value = temp;
      sessionCache.setJSON('breadcrumbs', breadcrumbs.value);
    }
  };

  /**
   * 设置动态路由添加状态
   * @param {boolean} status - 是否已添加
   */
  const setDynamicAddedRoute = (status) => {
    isDynamicAddedRoute.value = status;
  };

  /**
   * 设置用户菜单
   * @param {Array} menus - 菜单数据
   */
  const setMenuList = (menus) => {
    console.log('设置菜单列表:', menus);
    menuList.value = menuList.value.concat(menus);
    // sessionCache.setJSON('menuList', menuList.value);
  };

  /**
   * 设置菜单页控件权限
   * @param {Array} permissions - 权限数据
   */
  const setPermissions = (permissions) => {
    permissionList.value = permissions || [];
    // sessionCache.setJSON('permissions', permissions);
  };


  /**
   * 重置状态
   */
  const resetState = () => {
    menuState.collapsed = false;
    menuState.expandedKeys = [];
    breadcrumbs.value = [];
    isDynamicAddedRoute.value = false;
    menuList.value = [DEFAULT_MENU_ITEM];
    permissionList.value = [];
    sessionCache.remove('menuState');
  };

  /**
   * 构建路由
   * @param {string} systemId - 子系统ID，用于过滤菜单
   * @returns {Promise<Array>} - 路由配置
   */
  const buildRoutesAction = async (systemId = 'main') => {
    try {
      if (!systemId) {
        console.error('构建路由失败: 系统ID不能为空');
        return []
      }
      //每次开始构建的时候 都先重置
      setDynamicAddedRoute(false);
      console.log('开始构建动态路由...子系统ID:', systemId);
      // 获取菜单数据 - 实际项目中应从API获取
      const userMenus = await fetchMenuData(systemId);
      console.log('获取菜单数据成功:', userMenus.length, '条菜单');
      // 保存菜单
      setMenuList(userMenus);
      // 获取权限数据 - 实际项目中应从API获取
      // const permissionCodes = await fetchPermissions();
      // setPermissions(permissionCodes);
      // 从菜单数据生成路由配置
      const routes = generateRoutesFromMenu(userMenus);
      console.log('生成动态路由成功:', routes.length, '条路由', routes);
      // 更新面包屑和菜单状态
      initState();
      return routes;
    } catch (error) {
      console.error('构建路由错误:', error);
      return [];
    }
  };

  /**
  * 根据菜单路径生成路由名称
  * @param {string} menuPath - 菜单路径字符串，可能包含连字符或为驼峰格式
  * @returns {string} - 返回转换后的路由名称，如果输入为空则返回空字符串
  */
  const getRouteName = (menuPath) => {
    if (!menuPath || menuPath.trim() === '') return '';
    let resName = '';
    //KxxxDxxx(不用处理·返回的格式) || kxx-dxx || kxxDxx
    if (menuPath.includes('-')) {
      resName = menuPath.split('-').map(i => {
        if (i) return i[0].toUpperCase() + i.slice(1);
      }).join('');
    } else {
      resName = menuPath[0].toUpperCase() + menuPath.slice(1);
    }
    return resName;
  }


  /**
   * 从菜单数据生成路由配置
   * @param {Array} menus - 菜单数据
   * @returns {Array} - 生成的路由配置
   */
  const generateRoutesFromMenu = (menus, routes = []) => {
    if (!menus || !menus.length) return [];

    menus.forEach(menuItem => {
      // 1. 提取关键信息
      const menuPath = (menuItem.path || menuItem.menuUrl || '').replace(/^\/|\/$|^\.\//, '');
      const isTopLevel = !menuItem.parentId || menuItem.parentId === '0';
      const hasChildren = menuItem.children && menuItem.children.length > 0;
      const isHtmlPage = menuItem.isHtml === 1;
      const hasDetailPage = menuItem.detailPage === 1;

      // 2. 创建基础路由配置
      const route = {
        path: isTopLevel ? `/${menuPath}` : menuPath,
        name: getRouteName(menuPath),
        meta: {
          title: menuItem.menuName || menuItem.title,
          icon: menuItem.icon || menuItem.imgUrl,
          menuCode: menuItem.menuId || menuItem.menuCode,
          hidden: menuItem.hidden || false,
        },
        children: []
      };
      // 3. 处理不同类型的路由
      if (hasChildren) {
        // 3.1 有子菜单的路由
        if (isTopLevel) {
          route.component = Layout; // 顶级菜单使用Layout
        }
        // 递归处理子菜单
        generateRoutesFromMenu(menuItem.children, route.children);
      } else {
        // 3.2 叶子节点菜单处理
        const metaTem = {
          ...route.meta,
          iframeUrl: isHtmlPage ? menuItem.path : '',
          menuCode: menuItem.menuId || menuItem.menuCode,
          keepAlive: menuItem.keepAlive || true, // 默认缓存
          permissionCom: menuItem.permissionCom || [],
          expandMark: menuItem.expandMark || [],
        };
        // 处理嵌入HTML页面路径
        if (isHtmlPage) route.path = route.path?.split('.html')[0];

        // 处理无子菜单的一级菜单
        if (isTopLevel) {
          // 一级叶子节点路由页面 - 使用Layout包装 实际页面托付给创建的子路由
          route.component = Layout;
          route.redirect = `/${menuPath}/index`;
          route.children = [
            {
              path: 'index',
              name: getRouteName(`${menuPath}-index`),
              component: loadComponent(menuItem.component, isHtmlPage),
              meta: metaTem
            }
          ];
          if (hasDetailPage) {
            let detailName = getRouteName(`${menuPath}-index-details`);
            let detailComponent = menuItem.component?.replace(/\/[^\/]+$/, `/${detailName}`);
            route.children.push({
              path: `${menuPath}-index/:id`,
              name: detailName,
              component: loadComponent(detailComponent),
            });
          }
        } else {
          // 普通子节点路由页面 - 直接使用组件
          route.component = loadComponent(menuItem.component, isHtmlPage);
          route.meta = metaTem
          // 处理含有详情页的叶子节点菜单
          if (hasDetailPage) {
            let detailName = getRouteName(menuPath + '-details');
            let detailComponent = menuItem.component?.replace(/\/[^\/]+$/, `/${detailName}`);
            routes.push({
              path: `${menuPath}/:id`,
              name: detailName,
              component: loadComponent(detailComponent),
            });
          }
        }
      }
      routes.push(route);
    });

    return routes;
  };

  /**
   * 加载组件 - 修复版本
   * @param {string} componentPath - 组件路径
   * @param {boolean} isFrame - 是否是iframe
   * @returns {Function} - 组件导入函数
   */
  const loadComponent = (componentPath, isFrame = false) => {
    // 1. 如果isFrame为true，则使用iframe模板页面
    if (isFrame) {
      return pageModules['../views/menuPage/iframe/IframePage.vue'];
    }
    //规范化路径，确保与glob匹配
    const path = `../views/menuPage${componentPath}.vue`;
    if (componentPath && pageModules[path]) {
      return pageModules[path];
    }
    // 3. 如果找不到预定义组件，则使用通用的导入方式
    return () => import('../views/emptyPage/empty.vue');
  };

  /**
   * 检查是否有指定权限
   * @param {string|Array} value - 权限标识
   * @returns {boolean} - 是否有权限
   */
  const hasPermission = (value) => {
    if (!value) return true;

    // 转为数组处理
    const permissions = permissionList.value;
    const permissionValues = Array.isArray(value) ? value : [value];

    // 超级管理员拥有所有权限
    if (permissions.includes('*')) {
      return true;
    }

    return permissionValues.some(v => permissions.includes(v));
  };

  /**
   * 获取菜单数据
   * @param {string} systemId - 子系统ID，用于过滤菜单
   * @returns {Promise<Array>} - 菜单数据
   */
  const fetchMenuData = (systemId) => {
    // 模拟后端API请求
    return new Promise((resolve) => {
      setTimeout(() => {
        // 定义所 有系统的菜单数据
        const allSystemMenus = {
          'main': [
            {
              menuId: 1000,
              parentId: 0,
              path: '/personal-office',
              menuName: '个人办公',
              icon: 'icon-office',
              children: [
                {
                  menuId: 1010,
                  parentId: 1000,
                  path: 'plan-Manage-audit',
                  menuName: '采购方案审核(框采)',
                  icon: '',
                  detailPage: 1,
                  component: '/SupervisionSys/personalOffice/PlanManageAudit',
                },
              ]
            },
            {
              menuId: 1100,
              parentId: 0,
              path: '/procurement-management',
              menuName: '采购项目管理',
              icon: 'icon-promanage',
              children: [
                {
                  menuId: 1110,
                  parentId: 1100,
                  path: 'flow-chart',
                  menuName: '流程图展示',
                  component: '/SupervisionSys/procurementManage/FlowChart',
                },
                {
                  menuId: 1120,
                  parentId: 1100,
                  path: 'project-query',
                  menuName: '采购项目立项',
                  component: '/SupervisionSys/procurementManage/ProjectQuery',
                  detailPage: 1,
                },
              ]
            },
            {
              menuId: 1200,
              parentId: 0,
              path: '/procurement-require-management',
              menuName: '采购需求管理',
              icon: 'icon-demand',
              children: [
                {
                  menuId: 1210,
                  parentId: 1200,
                  path: 'require-query',
                  menuName: '采购需求查询',
                  component: '/SupervisionSys/procurementRequireManagement/RequireQuery',
                  detailPage: 1,
                },

              ]

            },
            {
              menuId: 1300,
              parentId: 0,
              path: '/notice-management',
              menuName: '公告管理',
              icon: 'icon-notice',
              children: [
                {
                  menuId: 1310,
                  parentId: 1300,
                  path: 'procurement-notice',
                  menuName: '采购(征集)公告',
                  component: '/SupervisionSys/noticeManagement/ProcurementNotice',
                  detailPage: 1,
                },
                {
                  menuId: 1320,
                  parentId: 1300,
                  path: 'framework-agreement-notice',
                  menuName: '框架协议入围结果公告',
                  component: '/SupervisionSys/noticeManagement/FrameworkAgreementNotice',
                  detailPage: 1,
                }
              ]
            }

          ],
          'sub-sys2': [
            {
              menuId: 2100,
              parentId: 0,
              path: '/sub',
              menuName: '子菜单管理',
              icon: '',
              children: [
                {
                  menuId: 2110,
                  parentId: 2100,
                  path: 'sub-menu1',
                  menuName: '菜单一',
                  component: '/SubSys/subMenu/Menu1',
                  isHtml: 0
                },
                {
                  menuId: 2120,
                  parentId: 2100,
                  path: './html/platform/sys/user/userMgr.html',
                  menuName: '菜单二',
                  isHtml: 1
                }
              ]
            }
          ],
          'sub-sys3': [
            {
              menuId: 3100,
              parentId: 0,
              path: '/stats',
              menuName: '统计分析',
              icon: '',
              children: [
                {
                  menuId: 3110,
                  parentId: 3100,
                  path: '/data-visual',
                  menuName: '数据可视化',
                  children: [
                    {
                      menuId: 3111,
                      parentId: 3110,
                      path: '/data-visual/pie-chart',
                      menuName: '饼图',
                      component: '/statsAnalysis/DataVisual',
                    },
                  ]
                },
                {
                  menuId: 3120,
                  parentId: 3100,
                  path: '/report-export',
                  menuName: '报表导出',
                  component: ''
                }
              ]
            }
          ],
          'sub-sys4': [
            {
              menuId: 4100,
              parentId: 0,
              path: '/staff',
              menuName: '人员信息管理',
              icon: 'icon-mainbody',
              children: [
                {
                  menuId: 4110,
                  parentId: 4100,
                  path: '/unit-maintain',
                  menuName: '维护单位信息',
                  component: '/staffInfoManage/UnitMaintain',
                  permission: 'unit:manage'
                },
                {
                  menuId: 4120,
                  parentId: 4100,
                  path: '/department-maintain',
                  menuName: '维护部门信息',
                  component: '/staffInfoManage/DepartmentMaintain',
                },
                {
                  menuId: 4130,
                  parentId: 4100,
                  path: '/personnel-maintain',
                  menuName: '维护人员信息',
                  component: '/staffInfoManage/PersonnelMaintain',
                }
              ]
            }
          ]
        };

        // 根据系统ID返回对应的菜单
        const menuData = allSystemMenus[systemId] || [];
        resolve(menuData);
      }, 300);
    });
  };

  /**
   * 获取权限数据
   * @returns {Promise<Array>} - 权限数据
   */
  const fetchPermissions = () => {
    // 模拟后端API请求
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟后端返回的权限数据
        const permissionData = [
          // 按钮权限
          'btn:add:1210', 'btn:edit:1210', 'btn:delete:1210',
          'btn:add:1220', 'btn:edit:1220', 'btn:delete:1220',
          'btn:add:1230', 'btn:edit:1230', 'btn:delete:1230'
        ];

        resolve(permissionData);
      }, 100);
    });
  };

  return {
    // 状态
    menuList,
    permissionList,
    isDynamicAddedRoute,

    // 计算属性
    getMenuCollapsed,
    getBreadcrumbs,
    getExpandedKeys,
    getIsDynamicAddedRoute,
    getMenuList,

    // 方法
    setMenuCollapsed,
    setExpandedKeys,
    setBreadcrumbs,
    setDynamicAddedRoute,
    setMenuList,
    setPermissions,
    resetState,
    buildRoutesAction,
    hasPermission
  };
});