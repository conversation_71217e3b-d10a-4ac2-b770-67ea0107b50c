<template>
  <div class="notice-details-page">
    <div
      class="flex flex-col h-full"
      ref="pageRef"
    >
      <a-page-header
        :ghost="false"
        @back="handleBack"
      >
        <template #title>
          <span class="page-header-title">采购征集公告详情</span>
        </template>
      </a-page-header>
      <div
        class="white-space"
        style="height: 6px;"
      ></div>

      <div class="flex-1 overflow-x-hidden overflow-y-auto">
        <div class="p-6 bg-white rounded-md shadow-sm">
          <div class="mb-8 text-center">
            <h1 class="text-xl text-gray-800"><a-tag color="warning">{{ noticeData.area }}</a-tag>{{
              noticeData.title }}</h1>
            <div class="mt-3 text-gray-500">发布时间：{{ noticeData.publishTime }}</div>
          </div>

          <section-title title="一、项目概况" />
          <div class="content-section">
            <div class="mb-4">
              <p>湖南省各级预算单位2024年工作站、通用服务器框架协议采购项目的潜在响应供应商应在湖南省政府采购网获取征集文件，并于 2024年12月30日 09时00分00秒（北京时间）前递交响应文件。</p>
            </div>
          </div>

          <section-title title="二、征集邀请" />
          <div class="content-section">
            <div class="mb-4">
              <p>湖南省公共资源交易中心拟对湖南省各级预算单位2024年工作站、通用服务器框架协议采购项目项目进行框架协议采购征集，兹邀请符合本次征集要求的供应商参加征集。</p>
            </div>
          </div>

          <section-title title="三、征集项目概述" />
          <div class="content-section">
            <info-item
              label="1、征集项目名称"
              :value="noticeData.projectName"
            />
            <info-item
              label="2、征集项目编号"
              :value="noticeData.projectCode"
            />
            <info-item
              label="3、征集项目简介"
              :value="noticeData.projectIntro"
              :is-title="true"
            />
            <p class="mb-2 ml-4">根据财政部、工信部2023年12月26日发布的一系列《政府采购需求标准》（财库〔2023〕32号、33号），对以下品目开展框架协议采购： 1.工作站； 2.通用服务器。
            </p>
            <info-item
              label="4、采购需求"
              :is-title="true"
            />
            <p class="mb-2 ml-4">采购内容：</p>
          </div>

          <div class="mx-4 mt-4 overflow-x-auto">
            <table class="data-table">
              <thead>
                <tr>
                  <th>序号</th>
                  <th>采购包号</th>
                  <th>标的名称</th>
                  <th>采购品目</th>
                  <th>计量单位</th>
                  <th>所属行业</th>
                  <th>是否采购进口产品</th>
                  <th>采购节能产品</th>
                  <th>采购节能环保产品</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(item, index) in noticeData.procurementList"
                  :key="index"
                >
                  <td>{{ item.seq }}</td>
                  <td>{{ item.packageNo }}</td>
                  <td>{{ item.targetName }}</td>
                  <td>{{ item.item }}</td>
                  <td>{{ item.unit }}</td>
                  <td>{{ item.industry }}</td>
                  <td>{{ item.isImport }}</td>
                  <td>{{ item.isEnergySaving }}</td>
                  <td>{{ item.isEcoFriendly }}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <section-title title="四、供应商资格要求" />
          <div class="content-section">
            <info-item
              label="1、资格要求"
              :is-title="true"
            />
            <p class="mb-2 ml-4">(1) 符合《中华人民共和国政府采购法》第二十二条规定的条件；</p>
            <p class="mb-2 ml-4">(2) 落实政府采购政策需满足的资格要求：无;</p>
            <p class="mb-2 ml-4">(3) 本项目的特定资格要求：无。</p>

            <info-item
              label="2、响应要求"
              :is-title="true"
            />
            <p class="mb-2 ml-4">符合资格条件的供应商应当在规定的时间内提交响应文件，并对响应文件的真实性、合法性承担法律责任。</p>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

// 路由相关
const router = useRouter();
const route = useRoute();

// 返回上一页
const handleBack = () => {
  router.back();
};

// 公告数据
const noticeData = reactive({
  area: '省本级',
  title: '湖南省公共资源交易中心湖南省各级预算单位2024年工作站、通用服务器框架协议采购项目征集公告',
  publishTime: '2024-12-04 16:23:07',
  projectName: '湖南省各级预算单位2024年工作站、通用服务器框架协议采购项目',
  projectCode: 'K43000000020240000002',
  projectIntro: '根据财政部、工信部2023年12月26日发布的一系列《政府采购需求标准》（财库〔2023〕32号、33号），对以下品目开展框架协议采购： 1.工作站； 2.通用服务器。',
  purchaserName: '湖南省公共资源交易中心',
  contactPerson: '业务二部',
  contactWay: '0731-89665152、0731-89665170',
  address: '湖南省长沙市雨花区万家丽南路二段29号',
  fileTime: '2024年12月04日至2024年12月30日，每天上午09:00至12:00，下午14:30至17:30（北京时间，法定节假日除外）',
  filePlace: '湖南省政府采购网',
  deadlineTime: '2024年12月30日09时00分00秒（北京时间）',
  submissionPlace: '湖南省政府采购网',
  openingTime: '2024年12月30日09时00分00秒（北京时间）',
  openingPlace: '湖南省公共资源交易中心',
  otherMatters: '无',
  agencyName: '湖南省公共资源交易中心',
  agencyAddress: '湖南省长沙市雨花区万家丽南路二段29号',
  agencyContact: '0731-89665152、0731-89665170',
  procurementList: [
    {
      seq: '1',
      packageNo: '1',
      targetName: '工作站',
      item: '工作站',
      unit: '台',
      industry: '计算机设备',
      isImport: '否',
      isEnergySaving: '是',
      isEcoFriendly: '是'
    },
    {
      seq: '2',
      packageNo: '2',
      targetName: '通用服务器',
      item: '服务器',
      unit: '台',
      industry: '计算机设备',
      isImport: '否',
      isEnergySaving: '是',
      isEcoFriendly: '是'
    }
  ]
});

// 标题组件
const SectionTitle = (props) => {
  return h('h2', {
    class: 'text-base pl-3 mb-2 mt-5',
    style: {
      fontFamily: 'AlibabaPuHuiTi-bold'
    }
  }, props.title);
};

// 信息项组件
const InfoItem = (props) => {
  const isTitle = props.isTitle || false;

  return h('p', {
    class: 'mb-2'
  }, [
    h('span', {
      class: isTitle ? 'font-medium block mb-2' : 'font-medium'
    }, `${props.label}${isTitle ? '' : '：'}`),
    !isTitle && h('span', null, props.value)
  ]);
};

onMounted(() => {
  // 获取详情数据
  const { id } = route.params;
  const { prjName } = route.query;
  console.log('公告ID:', id);
  console.log('项目名称:', prjName);
  noticeData.projectName = prjName;
  noticeData.title = prjName + '征集公告';
  // 这里应该调用实际的详情API获取数据
  // const fetchNoticeDetails = async () => {
  //   try {
  //     const res = await getNoticeDetails(id);
  //     if (res.code === '0000') {
  //       Object.assign(noticeData, res.data);
  //     }
  //   } catch (error) {
  //     console.error('获取详情失败:', error);
  //   }
  // };
  // fetchNoticeDetails();
});
</script>

<style lang="scss" scoped>
.notice-details-page {
  height: 100%;
  @include custom-details-page;
}

.content-section {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.data-table {
  width: 100%;
  border-collapse: collapse;

  th,
  td {
    border: 1px solid #e2e8f0;
    padding: 0.75rem 1rem;
    text-align: center;
  }

  th {
    background-color: #f7fafc;
  }

  td.text-left {
    text-align: left;
  }

  tr:hover {
    background-color: #f8fafc;
  }
}
</style>