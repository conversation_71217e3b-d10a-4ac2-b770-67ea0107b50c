<template>
  <div id="layout-container">
    <!-- <a-spin
      :spinning="appState.loading"
      :tip="appState.loadingTip"
      style="width: 100%; height: 100%;"
      size="large"
    > -->
    <HomeHeader />
    <div class="main-box">
      <aside :class="{ 'aside_close': appState.sidebarCollapsed, 'aside_expand': !appState.sidebarCollapsed }">
        <Sidebar
          :menu-list="menuList"
          :collapsed="appState.sidebarCollapsed"
          @update:collapsed="handleSidebarCollapse"
          @menu-click="handleMenuClick"
        />
      </aside>
      <main>
        <Tabs :is-scrolled="appState.isPageScrolled" />
        <div
          class="main-page"
          ref="mainPageRef"
          @scroll="handleMainPageScroll"
        >
          <router-view v-slot="{ Component }">
            <Transition
              name="fade-slide"
              mode="out-in"
              appear
            >
              <keep-alive
                :include="cachedViews"
                :max="6"
              >
                <component
                  :is="Component"
                  :key="$route.fullPath"
                />
              </keep-alive>
            </Transition>
          </router-view>
        </div>
      </main>
    </div>

  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/useUserStore.js';
import { useMenuStore } from '@/stores/useMenuStore.js';
import { useTabsStore } from '@/stores/useTabsStore.js';
import { useGlobalCacheStore } from '@/stores/useGlobalCacheStore.js';
import { notification } from 'ant-design-vue';
import { getPeriod } from '@/utils/tools.js';
import { frontTip } from '@/common/js/pub-methods';
import emitter from '@/utils/mitt.js';
import navigateService from '@/utils/navigate.js';
// 导入布局组件
import HomeHeader from '@/layout/header/home-header.vue';
import Sidebar from '@/layout/siderbar/sidebar.vue';
import Tabs from '@/layout/tabs/tabs.vue';

// 通知组件配置
notification.config({
  top: '90px',
  duration: 3
});

// Store实例
const userStore = useUserStore();
const menuStore = useMenuStore();
const tabsStore = useTabsStore();
const globalCacheStore = useGlobalCacheStore();

// 应用状态
const appState = reactive({
  loading: false,
  loadingTip: '系统初始加载中,请稍后...',
  hasError: false,
  sidebarCollapsed: menuStore.getMenuCollapsed,
  isPageScrolled: false, // 页面是否已滚动
});

// 主页面引用
const mainPageRef = ref(null);



// 缓存的视图组件名称
const cachedViews = computed(() => {
  const tabs = tabsStore.getTabs;
  let resCachedViews = tabs
    .map(tab => {
      // 查找路由配置是否设置了keepAlive
      const routeConfig = navigateService.findRouteByMenuCode(tab.menuCode);
      return routeConfig?.meta?.keepAlive === true ? routeConfig.name : '';
    })
  // console.log('xxxxxxxx缓存组件', resCachedViews);
  return resCachedViews

});


// 菜单列表 - 从用户权限中获取
const menuList = computed(() => {
  // 从菜单store中获取菜单数据
  return menuStore.getMenuList || [];
});

/**
 * 处理菜单点击
 * @param {Object} menuItem 菜单项对象
 */
const handleMenuClick = (menuItem) => {
  try {
    console.log('点击菜单项:', menuItem);
    // 兼容menuId和menuCode字段
    const menuCode = menuItem.menuId || menuItem.menuCode;
    if (!menuCode) {
      console.warn('菜单项缺少menuId或menuCode字段:', menuItem);
      return;
    }
    // 直接使用导航服务处理所有菜单点击，包括工作台
    navigateService.navigateByMenuCode(menuCode);
  } catch (error) {
    console.error('处理菜单点击错误:', error);
  }
};

/**
 * 处理侧边栏折叠状态变更
 * @param {boolean} collapsed 是否折叠
 */
const handleSidebarCollapse = (collapsed) => {
  appState.sidebarCollapsed = collapsed;
  menuStore.setMenuCollapsed(collapsed);
};

/**
 * 登录成功提示
 */
const showWelcomeNotice = () => {
  const periodText = getPeriod() || '您';
  const username = userStore.getUserName || '用户';
  notification.open({
    message: '欢迎回来',
    description: `${username}，${periodText}好！`,
    style: { width: '280px' },
    duration: 2,
  });
};
const setCollapsedFromWindowSize = () => {
  let docHeight = window.innerWidth || document.documentElement.clientHeight;
  if (docHeight < 1300) {
    appState.sidebarCollapsed = true;
  } else {
    appState.sidebarCollapsed = false;
  }
};
/**
 * 初始化页面
 */
onMounted(() => {
  // 加载中状态
  appState.loading = true;
  // appState.loadingTip = '系统初始加载中,请稍后...';
  try {
    // 设置事件监听
    setCollapsedFromWindowSize();
    setupEventListeners();
    // 显示欢迎提示
    showWelcomeNotice();
    // 初始化标签页
    tabsStore.initState();
    // 尺寸监听
    window.onresize = setCollapsedFromWindowSize;
  } catch (error) {
    console.error('初始化错误:', error);
    appState.hasError = true;
    frontTip('error', '系统初始化失败，请刷新页面重试');
  } finally {
    // 关闭加载状态
    appState.loading = false;
  }

}
);


/**
 * 处理主页面滚动事件
 */
const handleMainPageScroll = () => {
  if (!mainPageRef.value) return;
  // 检查滚动位置，如果滚动了就添加阴影
  appState.isPageScrolled = mainPageRef.value.scrollTop > 0;
};

/**
 * 设置事件总线监听
 */
const setupEventListeners = () => {
  // 添加标签页
  emitter.on('addTab', (e) => {
    const { menuCode, query } = e;
    // 导航到URL或菜单代码
    if (menuCode) {
      navigateService.navigateByMenuCode(menuCode, query);
    }
  });

  // 移除标签页
  emitter.on('removeTab', ({ isBackMenuCode, query }) => {
    if (isBackMenuCode) {
      // 尝试导航回指定路径
      navigateService.navigateByMenuCode(isBackMenuCode, query, true);
    } else {
      // 回退到工作台
      navigateService.navigateToWorkplace();
    }
  });
};

// 监听侧边栏折叠状态
watch(() => appState.sidebarCollapsed, (newVal) => {
  menuStore.setMenuCollapsed(newVal);
});

// 组件卸载前清理事件监听
onBeforeUnmount(() => {
  emitter.off('addTab');
  emitter.off('removeTab');
});
</script>

<style lang="scss" scoped>
#layout-container {
  width: 100%;
  height: 100%;
  background-color: $primary-bg-color;
  overflow: hidden;
  position: relative;

  .main-box {
    display: flex;
    height: calc(100% - $header-height);

    aside {
      background-color: $bg-color-sidebar;
      transition: all .35s;
      position: relative;
      color: $text-color-sidebar;

      .ant-badge {
        color: inherit;
      }

      :deep(.ant-menu) {

        &:not(.ant-menu-sub) {
          padding: 12px 0 48px 0;
          width: 100%;
          height: 100%;
          overflow-y: auto;

          .ant-menu-inline {
            background-color: $bg-color-submenu-line;
          }

          // 定制化滚动条
          &::-webkit-scrollbar {
            width: 5px;
            height: 5px;
          }

          &::-webkit-scrollbar-thumb {
            background-color: $bg-color-sidebar-menu-scrollbar;
            background-clip: none;
            border: none;
          }

          &::-webkit-scrollbar-track {
            background-color: transparent;
          }
        }



        .ant-menu-item-selected {
          position: relative;
          background-color: $bg-color-sidebar-menu-active;

          .ant-menu-title-content {
            font-family: $font-title;
          }

          &::before {
            display: inline-block;
            content: '';
            width: 4px;
            height: 100%;
            position: absolute;
            left: 0;
            z-index: 1;
            background-color: $bg-color-sidebar-menu-active-prefixL;
          }
        }
      }
    }

    .aside_close {
      width: $sidebar-collapsed-width;
    }

    .aside_expand {
      width: $sidebar-width;
    }

    main {
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: hidden;
      /* 防止整体溢出 */

      .main-page {
        flex: 1;
        /* 占据剩余空间 */
        padding: 7px 8px 10px 8px;
        height: calc(100% - $tabs-bar-height);
        overflow: auto;

      }

      .error-message {
        padding: 8px 16px;
        font-size: 16px;

        a {
          text-decoration: underline;
          cursor: pointer;
        }
      }
    }
  }

  /* 加载样式容器大小调整 */
  .ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
      height: 100%;
    }
  }

}

@media screen and (min-width: 1600px) {
  #layout-container {
    .main-box {
      height: calc(100% - $header-height-lg);

      main .main-page {
        height: calc(100% - $tabs-bar-height);
        /* 大屏幕下也更新高度计算 */
      }
    }
  }
}
</style>
