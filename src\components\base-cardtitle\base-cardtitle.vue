<template>
  <div
    id="base-cardtitle-wrapper"
    v-bind="$attrs"
  >
    <div class="main-content">
      <div class="left-title_wrapper">
        <span
          class="title"
          :style="{ fontSize: getFontSize(props.size) }"
        >{{ props.title }}</span>
        <span class="desc">
          <slot name="expand-desc">

          </slot>
        </span>
      </div>
      <div class="right-extra-wrapper">
        <slot name="right-extra">
        </slot>
      </div>
    </div>
  </div>
  <a-divider
    v-if="props.dividerVisible"
    style="margin: 8px 0 0 0;"
  />
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  dividerVisible: {
    type: Boolean,
    default: true,
  },
  size: {
    type: String,
    default: 'large',
  }
});

const getFontSize = (size) => {
  if (size == 'large') {
    return '18px'
  } else if (size == 'small') {
    return '15px'
  } else {
    return '16px'
  }
}
</script>

<style lang="scss" scoped>
#base-cardtitle-wrapper {

  .main-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* padding-right: 12px; */

    .title {
      position: relative;
      padding-left: 16px;
      font-family: $font-title;

      &::before {
        display: inline-block;
        content: '';
        position: absolute;
        top: 15%;
        left: 4px;
        width: 4px;
        height: 70%;
        margin-right: 8px;
        background-color: $primary-color;
        border-radius: 2px;
      }

      .desc {
        margin-left: 4px;
        color: #f6a133;
        font-size: 12px;
      }
    }

  }

  :deep(.ant-divider) {
    margin-top: 10px;
  }
}
</style>