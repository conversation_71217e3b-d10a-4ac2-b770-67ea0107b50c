<template>
  <div class="project-details-page">
    <div
      class="flex flex-col h-full"
      ref="pageRef"
    >
      <a-page-header
        :ghost="false"
        @back="handleBack"
      >
        <template #title>
          <span class="page-header-title">采购方案详情</span>
        </template>
        <template #extra>
          <a-space>
            <a-button
              class="min-w-[128px]"
              @click="handleViewProcess"
              type="default"
            >
              <template #icon><eye-outlined /></template>
              流程查看
            </a-button>
            <a-button
              @click="handleReject"
              type="primary"
              danger
            >
              <template #icon><close-outlined /></template>
              驳回
            </a-button>
            <a-button
              @click="handleApprove"
              type="primary"
              class="bg-green-500 border-green-500 hover:bg-green-600 hover:border-green-600 focus:bg-green-600 focus:border-green-600"
            >
              <template #icon><check-outlined /></template>
              通过
            </a-button>
          </a-space>
        </template>
      </a-page-header>
      <div
        class="white-space"
        style="height: 6px;"
      ></div>
      <div class="px-6 bg-white">
        <a-tabs v-model:activeKey="activeTabKey">
          <a-tab-pane
            key="basicInfo"
            tab="项目基本信息"
          ></a-tab-pane>
          <a-tab-pane
            key="packageInfo"
            tab="采购包信息"
          ></a-tab-pane>
          <a-tab-pane
            key="attachmentInfo"
            tab="附件信息"
          ></a-tab-pane>
        </a-tabs>
      </div>
      <div
        class="white-space"
        style="height: 6px;"
      ></div>
      <div class="flex-1 overflow-x-hidden overflow-y-auto pyy-4">
        <!-- 项目基本信息 -->
        <div
          v-show="activeTabKey === 'basicInfo'"
          class="animate__animated animate__fadeIn animate__faster"
        >
          <!-- 方案信息 -->
          <a-card :bordered="false">
            <template #title>
              <BaseCardtitle
                title="方案信息"
                size="small"
                :divider-visible="false"
              />
            </template>
            <a-descriptions
              :column="2"
              bordered
              size="middle"
              :labelStyle="{ fontWeight: 500 }"
            >
              <a-descriptions-item
                label="采购方案名称"
                :span="1"
              >{{ planInfo.planName }}</a-descriptions-item>
              <a-descriptions-item label="采购项目编号">{{ planInfo.projectCode }}</a-descriptions-item>
              <a-descriptions-item label="采购方案编号">{{ planInfo.planCode }}</a-descriptions-item>
              <a-descriptions-item label="框架协议采购类型">{{ planInfo.purchaseType }}</a-descriptions-item>
              <a-descriptions-item label="框架协议期限(月)">{{ planInfo.periodMonth }}</a-descriptions-item>
              <a-descriptions-item label="采购时间">{{ planInfo.purchaseTime }}</a-descriptions-item>
              <a-descriptions-item
                label="框架协议采购适用的法定情形"
                :span="2"
              >{{ planInfo.legalSituation }}</a-descriptions-item>
              <a-descriptions-item
                :span="2"
                label="适用框架协议的采购人或服务对象范围"
              >{{ planInfo.applicableScope }}</a-descriptions-item>
              <a-descriptions-item
                label="本轮框架协议采购履约管理措施"
                :span="2"
              >{{ planInfo.managementMeasures }}</a-descriptions-item>
              <a-descriptions-item
                label="其他事项"
                :span="2"
              >{{ planInfo.otherMatters }}</a-descriptions-item>
            </a-descriptions>
          </a-card>
          <div class="white-space"></div>

          <!-- 征集人信息 -->
          <a-card :bordered="false">
            <template #title>
              <BaseCardtitle
                title="征集人信息"
                size="small"
                :divider-visible="false"
              />
            </template>
            <a-descriptions
              :column="2"
              bordered
              size="middle"
              :labelStyle="{ fontWeight: 500 }"
            >
              <a-descriptions-item label="征集人名称">{{ collectorInfo.name }}</a-descriptions-item>
              <a-descriptions-item label="征集人类型">{{ collectorInfo.type }}</a-descriptions-item>
              <a-descriptions-item label="征集人联系人">{{ collectorInfo.contactPerson }}</a-descriptions-item>
              <a-descriptions-item label="征集人联系电话">{{ collectorInfo.contactPhone }}</a-descriptions-item>
            </a-descriptions>
          </a-card>
        </div>

        <!-- 采购包信息 -->
        <div
          v-show="activeTabKey === 'packageInfo'"
          class="animate__animated animate__fadeIn animate__faster"
        >
          <a-card :bordered="false">
            <template #title>
              <BaseCardtitle
                title="采购包信息"
                size="small"
                :divider-visible="false"
              />
            </template>
            <a-table
              :dataSource="packageList"
              :columns="packageColumns"
              :pagination="false"
              rowKey="packageId"
              :rowClassName="(_record, index) => index % 2 === 0 ? 'bg-white' : 'bg-gray-50'"
              size="middle"
              bordered
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'operation'">
                  <a
                    class="text-blue-500 hover:text-blue-600"
                    @click="handleViewPackage(record)"
                  >查看</a>
                </template>
                <template v-if="column.dataIndex === 'isImport'">
                  {{ text === '是' ? '是' : '否' }}
                </template>
                <template v-if="column.dataIndex === 'isSme'">
                  {{ text === '是' ? '是' : '否' }}
                </template>
              </template>
            </a-table>
          </a-card>
        </div>

        <!-- 附件信息 -->
        <div
          v-show="activeTabKey === 'attachmentInfo'"
          class="animate__animated animate__fadeIn animate__faster"
        >
          <file-upload-table
            :attachment-types="attachmentTypes"
            v-model:uploaded-files="uploadedFiles"
            :disabled="true"
          />
        </div>
      </div>
    </div>

    <!-- 包信息弹窗 -->
    <package-info-modal
      v-model:visible="packageModalVisible"
      :package-id="selectedPackageId"
    />

    <!-- 审批弹窗 -->
    <approval-modal
      v-model:visible="approvalModalVisible"
      :type="approvalType"
      :project-id="route.query.id || ''"
      @confirm="handleApprovalConfirm"
    />
  </div>
</template>

<script setup>
import { EyeOutlined, DownloadOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons-vue';
import { frontTip, popConfirm } from "@/common/js/pub-methods.js";
import PackageInfoModal from './components/PackageInfoModal.vue';
import ApprovalModal from './components/ApprovalModal.vue';

const router = useRouter();
const route = useRoute();

// 当前激活的标签页
const activeTabKey = ref('basicInfo');

// 包信息弹窗控制
const packageModalVisible = ref(false);
const selectedPackageId = ref(null);

// 审批弹窗控制
const approvalModalVisible = ref(false);
const approvalType = ref('approve'); // 'approve' 或 'reject'

// 方案信息
const planInfo = ref({
  planName: '永州市预算单位2024年度便式计算机、一体式计算机和机顶盒协议采购项目',
  projectCode: '永招[2023]第00042号',
  planCode: '永招[2023]第00042号',
  purchaseType: '刘师式',
  periodMonth: '24',
  purchaseTime: '2024-04-04',
  legalSituation: '（一）集中采购目录内商品，以及与之契合的监管需求',
  applicableScope: '湖南省各级预算单位',
  managementMeasures: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
  otherMatters: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'
});

// 征集人信息
const collectorInfo = ref({
  name: '永州市公共资源交易中心',
  type: '代理机构',
  contactPerson: '陈江南',
  contactPhone: '13337733333'
});

// 附件类型列表
const attachmentTypes = ref([
  {
    id: '1',
    attachmentName: '需求调查附件',
    uploadFormat: '[doc,zip,pdf]',
    required: true
  },
  {
    id: '2',
    attachmentName: '其他附件',
    uploadFormat: '[doc,zip,pdf]',
    required: false
  }
]);

// 已上传的附件列表
const uploadedFiles = ref([
  {
    attachmentTypeId: '1',
    fileId: '101',
    fileName: '采购计划书XXX.pdf',
    fileSize: '2.5MB',
    uploadTime: '2024-04-04 10:30:45',
    uploader: '张三'
  },
  {
    attachmentTypeId: '2',
    fileId: '102',
    fileName: '补充信息.pdf',
    fileSize: '1.2MB',
    uploadTime: '2024-04-04 11:15:20',
    uploader: '张三'
  }
]);

// 采购包列表
const packageList = ref([
  {
    packageId: '1',
    planCode: '审标通过运认中朝校放',
    packageCode: '包1',
    packageName: '标式计算机',
    purchaseType: '货物',
    purchaseMethod: '框架协议采购',
    evaluationMethod: '质量优先',
    isImport: '否',
    isSme: '是',
    firstEntryRate: '20%',
    secondStageMethod: '直接选定，顺序轮候'
  },
  {
    packageId: '2',
    planCode: '审标通过运认中朝校放',
    packageCode: '包2',
    packageName: '标式计算机',
    purchaseType: '货物',
    purchaseMethod: '框架协议采购',
    evaluationMethod: '质量优先',
    isImport: '否',
    isSme: '否',
    firstEntryRate: '20%',
    secondStageMethod: '直接选定，顺序轮候'
  }
]);

// 采购包表格列定义
const packageColumns = [
  {
    title: '采购计划编号',
    dataIndex: 'planCode',
    align: 'center',
    width: 150
  },
  {
    title: '包号',
    dataIndex: 'packageCode',
    align: 'center',
    width: 80
  },
  {
    title: '包名称',
    dataIndex: 'packageName',
    align: 'center',
    width: 120
  },
  {
    title: '采购类型',
    dataIndex: 'purchaseType',
    align: 'center',
    width: 100
  },
  {
    title: '采购方式',
    dataIndex: 'purchaseMethod',
    align: 'center',
    width: 120
  },
  {
    title: '评审规则',
    dataIndex: 'evaluationMethod',
    align: 'center',
    width: 100
  },
  {
    title: '是否涉及进口产品采购',
    dataIndex: 'isImport',
    align: 'center',
    width: 140
  },
  {
    title: '是否专门面向中小企业预留采购份额',
    dataIndex: 'isSme',
    align: 'center',
    width: 180
  },
  {
    title: '一阶段入围淘汰率',
    dataIndex: 'firstEntryRate',
    align: 'center',
    width: 120
  },
  {
    title: '二阶段成交方法',
    dataIndex: 'secondStageMethod',
    align: 'center',
    width: 140
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 80
  }
];

// 查看采购包详情
const handleViewPackage = (record) => {
  selectedPackageId.value = record.packageId;
  packageModalVisible.value = true;
};

// 返回上一页
const handleBack = () => {
  router.back();

};

// 查看流程
const handleViewProcess = () => {
  frontTip('info', '功能开发中...');
};

// 驳回操作
const handleReject = () => {
  approvalType.value = 'reject';
  approvalModalVisible.value = true;
};

// 通过操作
const handleApprove = () => {
  approvalType.value = 'approve';
  approvalModalVisible.value = true;
};

// 处理审批确认
const handleApprovalConfirm = (data) => {
  // 显示成功提示
  frontTip('success', data.type === 'reject' ? '已驳回' : '已通过');
  // 返回上一页或其他操作
  setTimeout(() => {
    router.back();
  }, 1000);
};

// 在组件挂载后加载数据
onMounted(() => {
  // 这里可以添加获取详情数据的接口调用
});
</script>

<style lang="scss" scoped>
.project-details-page {
  height: 100%;
  // 详情页设计样式 
  @include custom-details-page;
}
</style>