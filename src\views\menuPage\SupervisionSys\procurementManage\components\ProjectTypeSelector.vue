<template>
  <a-modal
    v-model:visible="dialogVisible"
    title="项目执行方式选择"
    :maskClosable="false"
    :footer="null"
    :closable="true"
    :width="1100"
    style="top:60px"
    wrap-class-name="project-type-modal"
  >
    <div class="flex flex-col">
      <!-- 项目类型选择部分 -->
      <div class="px-4">
        <!-- 使用base-cardtitle组件 -->
        <base-cardtitle
          title="请选择本次项目启动的类型"
          :dividerVisible="false"
          size="medium"
          class="mb-5"
        />

        <div class="grid grid-cols-5 gap-4 animate__animated animate__fadeIn animate__faster">
          <div
            class="relative flex flex-col items-center p-4 transition-all duration-300 border rounded-md cursor-pointer bg-gradient-to-b from-blue-50 to-blue-100 hover:shadow-md"
            :class="{ 'border-2 border-blue-500 shadow-lg': selectedProjectType === '1', 'border-blue-200': selectedProjectType !== '1' }"
            @click="selectProjectType('1')"
          >
            <div
              class="absolute top-0 right-0 hidden lightning-icon"
              :class="{ 'block': selectedProjectType === '1' }"
            >
              <thunderbolt-outlined class="text-lg text-yellow-400" />
            </div>
            <div
              class="flex items-center justify-center w-20 h-20 mb-3 transition-all duration-300 bg-blue-200 rounded-full"
            >
              <shopping-outlined class="text-4xl text-blue-600" />
            </div>
            <span class="text-base font-medium text-center text-gray-700">一般项目采购</span>
          </div>

          <div
            class="relative flex flex-col items-center p-4 transition-all duration-300 border rounded-md cursor-pointer bg-gradient-to-b from-purple-50 to-purple-100 hover:shadow-md"
            :class="{ 'border-2 border-purple-500 shadow-lg': selectedProjectType === '2', 'border-purple-200': selectedProjectType !== '2' }"
            @click="selectProjectType('2')"
            v-if="isSolicitor"
          >
            <div
              class="absolute top-0 right-0 hidden lightning-icon"
              :class="{ 'block': selectedProjectType === '2' }"
            >
              <thunderbolt-outlined class="text-lg text-yellow-400" />
            </div>
            <div
              class="flex items-center justify-center w-20 h-20 mb-3 transition-all duration-300 bg-purple-200 rounded-full"
            >
              <apartment-outlined class="text-4xl text-purple-600" />
            </div>
            <span class="text-base font-medium text-center text-gray-700">框架协议采购<br />(一阶段)</span>
          </div>

          <div
            class="relative flex flex-col items-center p-4 transition-all duration-300 border rounded-md cursor-pointer bg-gradient-to-b from-cyan-50 to-cyan-100 hover:shadow-md"
            :class="{ 'border-2 border-cyan-500 shadow-lg': selectedProjectType === '3', 'border-cyan-200': selectedProjectType !== '3' }"
            @click="selectProjectType('3')"
          >
            <div
              class="absolute top-0 right-0 hidden lightning-icon"
              :class="{ 'block': selectedProjectType === '3' }"
            >
              <thunderbolt-outlined class="text-lg text-yellow-400" />
            </div>
            <div
              class="flex items-center justify-center w-20 h-20 mb-3 transition-all duration-300 rounded-full bg-cyan-200"
            >
              <block-outlined class="text-4xl text-cyan-600" />
            </div>
            <span class="text-base font-medium text-center text-gray-700">框架协议采购<br />(二阶段)</span>
          </div>

          <div
            class="relative flex flex-col items-center p-4 transition-all duration-300 border rounded-md cursor-pointer bg-gradient-to-b from-green-50 to-green-100 hover:shadow-md"
            :class="{ 'border-2 border-green-500 shadow-lg': selectedProjectType === '4', 'border-green-200': selectedProjectType !== '4' }"
            @click="selectProjectType('4')"
          >
            <div
              class="absolute top-0 right-0 hidden lightning-icon"
              :class="{ 'block': selectedProjectType === '4' }"
            >
              <thunderbolt-outlined class="text-lg text-yellow-400" />
            </div>
            <div
              class="flex items-center justify-center w-20 h-20 mb-3 transition-all duration-300 bg-green-200 rounded-full"
            >
              <laptop-outlined class="text-4xl text-green-600" />
            </div>
            <span class="text-base font-medium text-center text-gray-700">电子卖场交易</span>
          </div>

          <div
            class="relative flex flex-col items-center p-4 transition-all duration-300 border rounded-md cursor-pointer bg-gradient-to-b from-indigo-50 to-indigo-100 hover:shadow-md"
            :class="{ 'border-2 border-indigo-500 shadow-lg': selectedProjectType === '5', 'border-indigo-200': selectedProjectType !== '5' }"
            @click="selectProjectType('5')"
          >
            <div
              class="absolute top-0 right-0 hidden lightning-icon"
              :class="{ 'block': selectedProjectType === '5' }"
            >
              <thunderbolt-outlined class="text-lg text-yellow-400" />
            </div>
            <div
              class="flex items-center justify-center w-20 h-20 mb-3 transition-all duration-300 bg-indigo-200 rounded-full"
            >
              <file-text-outlined class="text-4xl text-indigo-600" />
            </div>
            <span class="text-base font-medium text-center text-gray-700">采购合同录入</span>
          </div>
        </div>

        <!-- 项目流程介绍 -->
        <div
          v-if="selectedProjectType"
          class="mt-6"
        >
          <!-- 使用base-cardtitle组件 -->
          <base-cardtitle
            :title="`${projectTypeMap[selectedProjectType]}流程介绍`"
            :dividerVisible="false"
            size="medium"
            class="mb-5"
          />

          <!-- 步骤条 -->
          <div>
            <a-steps
              :current="processList[selectedProjectType].length"
              progress-dot
              class="custom-steps"
            >
              <a-step
                v-for="(step, index) in processList[selectedProjectType]"
                :key="index"
                :title="step.title"
                status="finish"
                :description="step.operator"
              />
            </a-steps>
          </div>

          <!-- 步骤详情 -->
          <div class="grid grid-cols-3 gap-4 mt-1">
            <div
              v-for="(step, index) in processList[selectedProjectType]"
              :key="index"
              class="p-3 border border-gray-200 rounded bg-gray-50 animate__animated animate__fadeIn"
            >
              <div class="flex items-center mb-2">
                <div
                  class="flex items-center justify-center w-6 h-6 mr-2 text-xs font-bold text-white bg-blue-500 rounded-full"
                >
                  {{ index + 1 }}
                </div>
                <div class="text-sm font-medium text-blue-600">{{ step.title }}</div>
              </div>
              <div class="ml-8 text-sm text-gray-600">{{ step.desc }}</div>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div
          class="flex items-center justify-end gap-4 pt-4 mt-6 border-t border-gray-200 animate__animated animate__fadeInUp animate__faster"
        >
          <a-button
            @click="dialogVisible = false"
            class="px-6 py-1 rounded h-9"
          >
            取消
          </a-button>
          <a-button
            type="primary"
            @click="handleConfirm"
            :disabled="!selectedProjectType"
            class="px-6 py-1 transition-all duration-300 bg-blue-500 rounded h-9 hover:bg-blue-600"
          >
            确认
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 框架协议选择器 -->
  <framework-agreement-selector
    v-model:visible="frameworkSelectorVisible"
    @select="handleFrameworkSelect"
  />
</template>

<script setup>
import { frontTip } from "@/common/js/pub-methods.js";
import {
  ShoppingOutlined,
  ApartmentOutlined,
  BlockOutlined,
  LaptopOutlined,
  FileTextOutlined,
  ThunderboltOutlined
} from '@ant-design/icons-vue';
import { PURCHASE_METHOD_OPTIONS } from '@/common/js/global.js';
import FrameworkAgreementSelector from './FrameworkAgreementSelector.vue';

//是否为征集人，只有征集人有框采一
const isSolicitor = ref(true);
const router = useRouter();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

// 内部弹窗状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 选择的项目类型 - 默认选择框架协议采购(一阶段)
const selectedProjectType = ref('2');

// 框架协议选择器可见性
const frameworkSelectorVisible = ref(false);


// 项目类型映射 - 基于全局常量，但进行必要的转换
const projectTypeMap = PURCHASE_METHOD_OPTIONS.reduce((acc, option) => {
  acc[option.value] = option.label;
  return acc;
}, {});

// 各项目类型对应的流程步骤
const processList = {
  // 一般项目采购
  '1': [
    {
      title: '采购项目立项',
      operator: '采购人',
      desc: '采购人通过系统发起项目立项，填写采购清单和预算等信息'
    },
    {
      title: '采购方案审批',
      operator: '采购人/采购方',
      desc: '采购部门和业务部门对采购方案进行审核和审批'
    },
    {
      title: '发布采购公告',
      operator: '采购人',
      desc: '在指定平台发布采购公告，供应商可查看并报名'
    },
    {
      title: '确定中标供应商',
      operator: '采购人',
      desc: '根据评审结果确定中标供应商并签订合同'
    }
  ],

  // 框架协议采购(一阶段)
  '2': [
    {
      title: '采购项目立项',
      operator: '征集人',
      desc: '征集人填写立项信息并提交，提交后从采购中台获取采购项目编号'
    },
    {
      title: '采购方案审批',
      operator: '征集人/采购办',
      desc: '征集人与采购办对采购方案进行审核(采购方案来自于框架协议采购系统)'
    },
    {
      title: '征集文件编制与征集公告',
      operator: '征集人/代理机构',
      desc: '代理机构在框架协议采购系统中编制采购需求与发布征集公告'
    },
    {
      title: '查看征集公告与征集文件',
      operator: '采购办/征集人',
      desc: '采购办/征集人在监管系统中查看征集公告与征集文件信息'
    },
    {
      title: '完成开评标流程',
      operator: '征集人/代理机构/供应商',
      desc: '各方交易主体在框架协议采购系统中完成开评标流程'
    },
    {
      title: '查看开评标信息与结果信息',
      operator: '采购办/征集人',
      desc: '查看征集项目的投标、开标、评标以及入围结果等信息'
    }
  ],

  // 框架协议采购(二阶段)
  '3': [
    {
      title: '订单需求确认',
      operator: '采购人',
      desc: '在已有框架协议下，确认具体采购订单需求'
    },
    {
      title: '邀请供应商报价',
      operator: '采购人',
      desc: '向框架协议内的供应商发出具体订单报价邀请'
    },
    {
      title: '评估选择供应商',
      operator: '采购人',
      desc: '根据报价和其他条件评估，选择本次订单供应商'
    },
    {
      title: '订单执行与验收',
      operator: '采购人/供应商',
      desc: '供应商执行订单，采购人进行验收确认'
    }
  ],

  // 电子卖场交易
  '4': [
    {
      title: '进入电子卖场',
      operator: '采购人',
      desc: '登录电子卖场系统，浏览产品目录'
    },
    {
      title: '选择商品',
      operator: '采购人',
      desc: '选择需要采购的商品加入购物车'
    },
    {
      title: '提交订单',
      operator: '采购人',
      desc: '确认订单信息，提交审批并完成支付'
    }
  ],

  // 采购合同录入
  '5': [
    {
      title: '合同信息录入',
      operator: '合同管理员',
      desc: '录入或导入采购合同基本信息和关键要素'
    },
    {
      title: '合同文件上传',
      operator: '合同管理员',
      desc: '上传合同扫描件和相关附件资料'
    },
    {
      title: '合同审核',
      operator: '审核人',
      desc: '相关部门对合同信息进行审核确认'
    },
    {
      title: '合同归档',
      operator: '合同管理员',
      desc: '完成合同归档，关联至对应采购项目'
    }
  ]
};

// 选择项目类型
const selectProjectType = (type) => {
  // 如果点击当前已选择的，则取消选择
  if (selectedProjectType.value === type) {
    selectedProjectType.value = '';
    return;
  }
  selectedProjectType.value = type;
};

// 确认选择
const handleConfirm = () => {
  if (!selectedProjectType.value) {
    frontTip('info', '请选择项目执行方式');
    return;
  }

  frontTip('success', `您选择了: ${projectTypeMap[selectedProjectType.value]}`);
  // 不同入口跳转新增
  if (selectedProjectType.value === '2') {
    handleJumpDetailsPage();
    dialogVisible.value = false;
  } else if (selectedProjectType.value === '3') {
    frameworkSelectorVisible.value = true;
  }
  // emit('confirm', selectedProjectType.value);

};
//跳转实施方式详情页
const handleJumpDetailsPage = async (params, cb = () => { }) => {
  await router.push({
    name: `ProjectQueryDetails`,
    params: {
      id: Math.random().toString(36).substring(2, 10),
    },
    //详情页类型
    query: {
      operateType: 'add',
      entrance: selectedProjectType.value || '',
      ...params  //其他参数
    }
  });
  if (cb && typeof cb === 'function') cb();
}
// 处理框架协议选择
const handleFrameworkSelect = (framework) => {
  console.log('handleFrameworkSelect', framework);
  const queryParams = {
    frameworkId: framework.id,
    frameworkCode: framework.frameworkCode,
    frameworkName: framework.frameworkName
  }
  handleJumpDetailsPage(queryParams, () => {
    dialogVisible.value = false;
    frameworkSelectorVisible.value = false;
  });

};



// 监听弹窗打开时设置默认选择
watch(() => props.visible, (newVal) => {
  if (newVal) {
    selectedProjectType.value = '2'; // 默认选择框架协议采购(一阶段)
  }
});
</script>

<style lang="scss">
.lightning-icon {
  position: absolute;
  top: 2px;
  right: 4px;
  z-index: 2;
}

/* 闪电图标动画效果 */
@keyframes flash {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.lightning-icon .anticon {
  animation: flash 2s infinite;
}

.project-type-modal {
  @include custom-modal;

  .custom-steps {
    .ant-steps-item {
      padding-bottom: 12px;
    }

    .ant-steps-item-title {
      font-size: 13px;
      font-weight: 500;
    }

    .ant-steps-item-description {
      font-size: 12px;
      color: #666;
    }

    .ant-steps-item-finish {
      .ant-steps-item-icon {
        .ant-steps-icon-dot {
          background-color: #3b82f6;
        }
      }
    }
  }
}
</style>