<template>
  <div
    class="login-header"
    :class="{ 'animate-in': isAnimated }"
  >
    <div class="header-content">
      <div class="logo-container">
        <!-- <span class="logo-icon"></span> -->
      </div>
      <div class="title-container">
        <div class="title-wrapper">
          <h1 class="system-title">{{ headerState.title?.endsWith('系统') ? headerState.title : headerState.title + '系统'
            }}</h1>
          <div class="badge-container">
            <!-- <span class="version-tag">{{ headerState.version }}</span> -->
          </div>
        </div>
        <p
          v-if="headerState.showSubtitle"
          class="system-subtitle"
        >{{ headerState.subtitle }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>

// 使用reactive管理头部数据
const headerState = reactive({
  title: import.meta.env.VITE_APP_TITLE || '基础搭建平台',
  subtitle: import.meta.env.VITE_APP_SUBTITLE || '',
  version: 'v1.0.0',
  showSubtitle: true // 默认不显示副标题，保持简洁
});

// 控制进场动画
const isAnimated = ref(false);

onMounted(() => {
  // 添加短暂延迟，确保动画效果可见
  setTimeout(() => {
    isAnimated.value = true;
  }, 100);
});
</script>

<style lang="scss" scoped>
.login-header {
  width: 100%;
  height: $header-height-login;
  padding: 0 40px;
  display: flex;
  align-items: center;
  position: relative;
  background-color: $bg-color-login-header;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);

  &.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  .header-content {
    // display: flex;
    // align-items: center;
    // gap: 16px;
    width: 100%;
  }

  .logo-container {
    display: flex;
    align-items: center;

    .logo-icon {
      width: 42px;
      height: 42px;
      @include base-icon(42px, 'icon-logo.png');
      transform: translateY(-2px);
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .title-container {
    display: flex;
    flex-direction: column;

    .title-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
    }

    .system-title {
      font-size: 34px;
      // font-weight: bold;
      color: #161515;
      margin: 0;
      font-family: $font-system-title;
      line-height: 1.2;
      letter-spacing: 0.5px;
      position: relative;
    }

    .badge-container {
      display: flex;
      align-items: center;
    }

    .version-tag {
      font-size: 12px;
      color: #1d50b8;
      background-color: rgba(29, 80, 184, 0.1);
      padding: 2px 8px;
      border-radius: 12px;
      font-family: $font-title;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(29, 80, 184, 0.15);
      }
    }

    .system-subtitle {
      font-size: 14px;
      color: #718096;
      margin: 0;
      letter-spacing: 0.5px;
      font-weight: normal;
      opacity: 0.8;
      text-align: center;
    }
  }
}

// 大屏幕调整
@media screen and (min-width: 1600px) {
  .login-header {
    height: $header-height-login-lg;
    padding: 0 50px;

    .logo-container .logo-icon {
      width: 48px;
      height: 48px;
    }

    .title-container {
      .system-title {
        font-size: 36px;
      }

      .version-tag {
        font-size: 14px;
      }

      .system-subtitle {
        font-size: 16px;
      }
    }
  }
}
</style>