import { ref, reactive, watch, toRefs, computed, nextTick, onMounted, onBeforeUnmount } from 'vue';
import { frontTip } from '@/common/js/pub-methods.js';
import { debounce } from 'lodash-es';

/**
 * 表格数据加载与筛选Hook
 * @param {Function} listRequestFn - 获取列表数据的请求函数
 * @param {Object} options - 配置选项
 * @returns {Object} - 返回表格相关状态和方法
 */
export function useTable (listRequestFn, options = {}) {
  // 解构配置选项，提供默认值
  const {
    initialFilter = {},           // 初始筛选条件
    initialPage = 1,              // 初始页码
    initialPageSize = 20,         // 初始每页条数
    autoLoad = true,              // 是否自动加载数据
    immediate = true,             // 是否立即监听变化
    pageField = 'pageNo',         // 分页字段名
    pageSizeField = 'pageSize',   // 每页条数字段名
    rowsField = 'rows',           // 数据行字段名
    totalField = 'total',         // 总数字段名
    debounceTime = 300,           // 防抖时间(毫秒)
    onError = null,               // 错误处理回调
    onSuccess = null,             // 成功处理回调
  } = options;

  // 表格数据状态
  const list = ref([]);           // 数据列表
  const loading = ref(false);     // 加载状态
  const curPage = ref(initialPage);       // 当前页码
  const pageSize = ref(initialPageSize);  // 每页条数
  const total = ref(0);           // 数据总数
  const error = ref(null);        // 错误信息
  const filterOption = reactive({ ...initialFilter }); // 筛选条件
  const sortInfo = reactive({     // 排序信息
    field: '',                    // 排序字段
    order: '',                    // 排序方向
  });

  // 计算属性：分页配置(用于ant-design-vue的Table组件)
  const pagination = computed(() => ({
    current: curPage.value,
    pageSize: pageSize.value,
    total: total.value,
    showSizeChanger: true,
    showQuickJumper: true,
    size: 'middle',
    showTotal: (total) => `共 ${total} 条记录`,
  }));

  // 加载数据
  const loadData = async () => {
    if (loading.value) return; // 防止重复请求

    loading.value = true;
    error.value = null;

    try {
      // 构建请求参数
      const params = {
        [pageField]: curPage.value,
        [pageSizeField]: pageSize.value,
        ...filterOption,
      };

      // 添加排序参数
      if (sortInfo.field && sortInfo.order) {
        params.sortField = sortInfo.field;
        params.sortOrder = sortInfo.order;
      }

      const res = await listRequestFn(params);

      // 更新数据
      list.value = res[rowsField] || [];
      total.value = res[totalField] || 0;

      // 调用成功回调
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess(res, list.value);
      }

      return res;
    } catch (err) {
      error.value = err;
      // 错误处理
      if (onError && typeof onError === 'function') {
        onError(err);
      } else {
        frontTip('error', '数据加载失败，请稍后重试', err);
      }
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 使用lodash-es的防抖函数
  const debouncedLoadData = debounce(() => {
    loadData();
  }, debounceTime, {
    //第一次就执行 防止后续连续点击
    leading: true,
    trailing: false,
  });

  // 监听分页和筛选条件变化
  if (immediate) {
    watch([curPage, pageSize], () => {
      debouncedLoadData();
    });

    watch(filterOption, () => {
      // 筛选条件变化时，重置为第一页
      curPage.value = initialPage;
      debouncedLoadData();
    }, { deep: true });

    watch(sortInfo, () => {
      debouncedLoadData();
    }, { deep: true });
  }

  // 设置筛选条件
  const setFilterOption = (obj = {}) => {
    Object.assign(filterOption, obj);
  };

  // 设置排序信息
  const setSort = (field, order) => {
    sortInfo.field = field;
    sortInfo.order = order;
  };

  // 处理表格变化(分页、排序等)
  const handleTableChange = (pag, _filters, sorter) => {
    // 处理分页变化
    if (pag) {
      curPage.value = pag.current;
      pageSize.value = pag.pageSize;
    }

    // 处理排序变化
    if (sorter && sorter.field) {
      sortInfo.field = sorter.field;
      sortInfo.order = sorter.order;
    } else {
      sortInfo.field = '';
      sortInfo.order = '';
    }

    // 不需要手动调用loadData，因为watch会触发加载
  };

  // 重置筛选条件和分页
  const reset = (loadAfterReset = true) => {
    // 重置筛选条件
    Object.keys(filterOption).forEach(key => {
      filterOption[key] = initialFilter[key] !== undefined ? initialFilter[key] : undefined;
    });

    // 重置分页
    curPage.value = initialPage;
    pageSize.value = initialPageSize;

    // 重置排序
    sortInfo.field = '';
    sortInfo.order = '';

    // 是否需要重新加载数据
    if (loadAfterReset) {
      // 使用nextTick确保状态更新后再加载数据
      nextTick(() => loadData());
    }
  };

  // 刷新当前页数据
  const refresh = () => {
    return loadData();
  };

  // 首次加载
  if (autoLoad) {
    loadData();
  }


  return {
    // 状态
    filterInfo: toRefs(filterOption), // 方便直接 v-model 绑定每个需要用到的属性
    list,
    loading,
    curPage,
    pageSize,
    total,
    error,
    pagination,
    sortInfo: toRefs(sortInfo),

    // 方法
    loadData,
    refresh,
    reset,
    setFilterOption,
    setSort,
    handleTableChange,
  };
}

/**
 * 表格高度自适应Hook
 * 用于计算表格内容区域的高度，使表格能够自适应容器高度
 *
 * @param {Object} options - 配置选项
 * @returns {Object} - 返回表格高度相关状态和方法
 */
export function useTableHeight (options = {}) {
  // 解构配置选项，提供默认值
  const {
    paginationHeight = 48,     // 分页控件高度
    minHeight = 150,           // 最小高度
    offsetHeight = 0,          // 额外偏移高度
    immediate = true,          // 是否在挂载后立即计算
  } = options;

  // 表格容器引用
  const tableWrapperRef = ref(null);
  // 表格内容区域高度
  const tableHeight = ref(400); // 默认高度，将在计算后更新

  // 计算表格高度
  const calculateTableHeight = () => {
    if (!tableWrapperRef.value) return;

    nextTick(() => {
      // 获取表格容器位置信息
      const rect = tableWrapperRef.value.getBoundingClientRect();
      // 获取表格容器的可用高度
      const containerHeight = rect.height;
      // 计算表格内容区域高度，考虑分页控件占用高度和额外偏移
      let contentHeight = containerHeight - paginationHeight - offsetHeight;
      // 确保高度不小于最小值
      contentHeight = Math.max(contentHeight, minHeight);

      // 只有当新计算的高度与当前高度不同时才更新，避免不必要的重渲染
      if (Math.abs(tableHeight.value - contentHeight) > 1) {
        tableHeight.value = contentHeight;
        // console.log('表格高度已更新:', contentHeight, '容器高度:', containerHeight);
      }
    });
  };

  // 防抖处理窗口大小变化
  const debouncedCalculateHeight = debounce(calculateTableHeight, 200);


  onMounted(() => {
    // 初始计算表格高度
    if (immediate) {
      calculateTableHeight();
    }
    window.addEventListener('resize', debouncedCalculateHeight);
  });

  onBeforeUnmount(() => {
    // 移除事件监听
    window.removeEventListener('resize', debouncedCalculateHeight);
  });


  return {
    tableWrapperRef,
    tableHeight,
    calculateTableHeight,
  };
}