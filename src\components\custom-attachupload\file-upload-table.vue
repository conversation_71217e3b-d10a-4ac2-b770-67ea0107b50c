<template>
  <div class="file-upload-table">
    <a-card :bordered="false">
      <template #title>
        <base-cardtitle
          title="附件信息"
          size="small"
          :divider-visible="false"
        >
          <template #expand-desc>
            <span class="p-2 mb-4 text-xs text-red-600 rounded upload-notice ">
              (说明：上传相关文件，大小不超过50M，请勿传密相关文件)
            </span>

          </template>
        </base-cardtitle>
      </template>
      <div class="pb-2">
        <a-table
          :dataSource="attachmentList"
          :columns="columns"
          :pagination="false"
          rowKey="id"
          :rowClassName="(_record, index) => index % 2 === 0 ? 'bg-white' : 'bg-gray-50'"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'attachmentName'">
              <div class="flex items-center">
                <span
                  class="mr-1 text-red-500"
                  v-if="record.required"
                >*</span>
                <span>{{ text }}</span>
              </div>
            </template>
            <template v-if="column.dataIndex === 'uploadFormat'">
              <span>{{ text }}</span>
            </template>
            <template v-if="column.dataIndex === 'fileName'">
              <span>{{ record.fileName || '-' }}</span>
            </template>
            <template v-if="column.dataIndex === 'uploadTime'">
              <span>{{ record.uploadTime || '-' }}</span>
            </template>
            <template v-if="column.dataIndex === 'uploader'">
              <span>{{ record.uploader || '-' }}</span>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <div class="flex items-center justify-center space-x-3">
                <template v-if="record.fileId">
                  <!-- 已上传状态 -->
                  <a
                    class="text-blue-500 cursor-pointer hover:text-blue-600"
                    @click="handleDownload(record)"
                  >
                    <download-outlined class="mr-1" />下载
                  </a>
                  <a-popconfirm
                    v-if="!disabled"
                    title="确定要删除此附件吗?"
                    ok-text="确定"
                    placement="topLeft"
                    cancel-text="取消"
                    @confirm="handleDelete(record)"
                  >
                    <a class="text-red-500 cursor-pointer hover:text-red-600">
                      <delete-outlined class="mr-1" />删除
                    </a>
                  </a-popconfirm>
                </template>
                <template v-else>
                  <!-- 未上传状态 -->
                  <a-upload
                    v-if="!disabled"
                    :showUploadList="false"
                    :beforeUpload="(file) => beforeUpload(file, record)"
                    :customRequest="(options) => customRequest(options, record)"
                  >
                    <a-button
                      type="link"
                      class="flex"
                      :loading="record.uploading"
                    >
                      <upload-outlined />上传
                    </a-button>
                  </a-upload>
                  <span
                    v-else
                    class="text-gray-400"
                  >-</span>
                </template>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { UploadOutlined, DownloadOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { frontTip } from "@/common/js/pub-methods.js";
import { message } from 'ant-design-vue';

const props = defineProps({
  // 附件列表数据
  attachmentTypes: {
    type: Array,
    default: () => []
  },
  // 已上传的附件数据
  uploadedFiles: {
    type: Array,
    default: () => []
  },
  // 是否禁用上传/删除功能
  disabled: {
    type: Boolean,
    default: false
  },
  // 最大文件大小(MB)
  maxSize: {
    type: Number,
    default: 50
  }
});

const emit = defineEmits(['update:uploadedFiles']);

// 表格列定义
const columns = [
  {
    title: '附件类型',
    dataIndex: 'attachmentName',
    align: 'center',
    width: 200
  },
  {
    title: '上传格式',
    dataIndex: 'uploadFormat',

    align: 'center',
    width: 200
  },
  {
    title: '附件名称',
    dataIndex: 'fileName',
    align: 'center',
    width: 200
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    align: 'center',
    width: 180
  },
  {
    title: '上传人',
    dataIndex: 'uploader',
    align: 'center',
    width: 120
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 150,
    align: 'center'
  }
];

// 合并附件类型和已上传文件数据
const attachmentList = computed(() => {
  // 创建一个新数组，包含所有附件类型
  const list = props.attachmentTypes.map(type => {
    // 查找该类型是否有已上传的文件
    const uploadedFile = props.uploadedFiles.find(file => file.attachmentTypeId === type.id);

    // 如果有已上传文件，合并信息
    if (uploadedFile) {
      return {
        ...type,
        ...uploadedFile,
        uploading: false
      };
    }

    // 如果没有已上传文件，只返回类型信息
    return {
      ...type,
      uploading: false
    };
  });

  return list;
});

// 上传前验证
const beforeUpload = (file, record) => {
  // 检查文件格式
  const formatStr = record.uploadFormat;
  // 只根据[doc,zip,execel,pdf]这种格式过滤
  const allowedFormats = formatStr.replace(/[\[\]\s]/g, '').split(',');

  // 获取文件扩展名
  const fileName = file.name;
  const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

  // 检查文件格式是否符合要求
  const isAllowedFormat = allowedFormats.some(format => {
    // 过滤空字符串
    if (!format) return false;
    return format.toLowerCase() === fileExt;
  });

  if (!isAllowedFormat) {
    frontTip('warn', `只能上传${formatStr}格式的文件!`);
    return false;
  }

  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize;
  if (!isLtMaxSize) {
    frontTip('warn', `文件大小不能超过${props.maxSize}MB!`);
    return false;
  }

  return true;
};

// 自定义上传请求
const customRequest = async ({ file, onSuccess, onError }, record) => {
  // 设置上传中状态
  const index = attachmentList.value.findIndex(item => item.id === record.id);
  if (index !== -1) {
    attachmentList.value[index].uploading = true;
  }
  const hide = message.loading('正在上传...', 0);
  try {
    // 这里应该调用实际的上传API
    // 模拟上传过程
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 模拟上传成功响应
    const response = {
      code: 200,
      data: {
        fileId: Date.now().toString(),
        fileName: file.name,
        fileSize: (file.size / (1024 * 1024)).toFixed(2) + 'MB',
        uploadTime: new Date().toLocaleString(),
        uploader: '当前用户'
      }
    };

    // 更新文件信息 - 只保留需要的字段
    const uploadedFile = {
      attachmentTypeId: record.id,
      fileId: response.data.fileId,
      fileName: response.data.fileName,
      fileSize: response.data.fileSize,
      uploadTime: response.data.uploadTime
    };

    // 更新已上传文件列表
    const newUploadedFiles = [...props.uploadedFiles];
    const existingIndex = newUploadedFiles.findIndex(f => f.attachmentTypeId === record.id);

    if (existingIndex !== -1) {
      newUploadedFiles[existingIndex] = uploadedFile;
    } else {
      newUploadedFiles.push(uploadedFile);
    }

    // 触发更新事件
    emit('update:uploadedFiles', newUploadedFiles);

    frontTip('success', '文件上传成功');
    onSuccess(response, file);
  } catch (err) {
    onError(err);
    frontTip('error', '上传失败');
  } finally {
    // 清除上传中状态
    const index = attachmentList.value.findIndex(item => item.id === record.id);
    if (index !== -1) {
      attachmentList.value[index].uploading = false;
    }
    hide();
  }
};

// 下载文件
const handleDownload = (record) => {
  if (!record.fileId) {
    frontTip('warn', '文件不存在');
    return;
  }

  // 这里应该调用实际的下载API
  const hide = message.loading('下载中...', 0);
  setTimeout(() => {
    hide();
    frontTip('success', `${record.fileName}下载成功`);
  }, 1500);
};

// 删除文件
const handleDelete = (record) => {
  // 这里应该调用实际的删除API
  // 模拟删除过程
  setTimeout(() => {
    // 从已上传文件列表中移除
    const newUploadedFiles = props.uploadedFiles.filter(
      file => file.attachmentTypeId !== record.id
    );

    // 触发更新事件
    emit('update:uploadedFiles', newUploadedFiles);

    frontTip('success', '附件删除成功');
  }, 500);
};
</script>

<style scoped>
.file-upload-table {
  width: 100%;
}
</style>
