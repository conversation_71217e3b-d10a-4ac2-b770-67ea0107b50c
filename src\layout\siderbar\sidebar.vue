<template>
  <div class="sidebar-container">
    <a-menu
      v-model:openKeys="openKeys"
      v-model:selectedKeys="selectedKeys"
      mode="inline"
      @openChange="onOpenMenuChange"
      :inline-collapsed="collapsed"
    >
      <template
        v-for="item in menuList"
        :key="item.menuId || item.menuCode"
      >
        <sidebar-item
          :menu-item="adaptMenuItem(item)"
          @menu-click="handleMenuClick"
        />
      </template>
      <div
        class="sidebar-footer"
        @click="toggleCollapse"
      >
        <span class="icon-collapsed"></span>
      </div>
    </a-menu>
  </div>
</template>

<script setup>
import { useMenuStore } from '@/stores/useMenuStore';
import { useTabsStore } from '@/stores/useTabsStore';
import SidebarItem from '@/layout/siderbar/sidebar-item.vue';
import navigateService from '@/utils/navigate.js';
import emitter from '@/utils/mitt.js';
const tabsStore = useTabsStore();
const props = defineProps({
  menuList: {
    type: Array,
    default: () => []
  },
  collapsed: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:collapsed', 'menu-click']);

const menuStore = useMenuStore();
const route = useRoute();

// 菜单展开项
const openKeys = ref(menuStore.getExpandedKeys);
const selectedKeys = ref([]);

// 当前选中的菜单项
const activeKey = computed(() => tabsStore.getActiveTab || '');

// 查找菜单的父路径
const findMenuPath = (code, list, path = []) => {
  if (!code || !list?.length) return [];
  for (const item of list) {
    // 当前菜单匹配
    if ((item.menuId || item.menuCode) === code) return path;
    // 有子菜单则递归查找
    if (item.children?.length) {
      const result = findMenuPath(code, item.children, [...path, item]);
      if (result.length) return result;
    }
  }
  return [];
};

// 确保父菜单展开
const ensureParentMenuExpanded = (code) => {
  if (!code) return;
  const parents = findMenuPath(code, props.menuList);
  if (parents.length) {
    const keys = [...openKeys.value, ...parents.map(m => m.menuId || m.menuCode)];
    openKeys.value = [...new Set(keys)];
    menuStore.setExpandedKeys(openKeys.value);
  }
};

// 监听展开的菜单项变化
const onOpenMenuChange = (keys) => {
  menuStore.setExpandedKeys(keys);
};

// 适配菜单项数据结构
const adaptMenuItem = (item) => {
  if (!item) return {};
  // 统一字段
  const result = {
    ...item,
    menuCode: item.menuId || item.menuCode,
    menuName: item.title || item.menuName,
    icon: item.icon
  };
  // 无menuCode但有路径时使用路径
  if (!result.menuCode && (item.path || item.menuUrl)) {
    result.menuCode = (item.path || item.menuUrl).replace(/^\//g, '');
  }
  // 处理子菜单
  if (item.children?.length) {
    // 等同于 item.children.map(child => adaptMenuItem(child))
    // 当传入的函数只有一个参数并且直接作为另一个函数的参数时，可以使用这种简写形式
    result.children = item.children.map(adaptMenuItem);
  }
  return result;
};

// 处理菜单点击
const handleMenuClick = (menuItem) => {
  // console.log('点击菜单项:', menuItem);
  emit('menu-click', menuItem);
};

// 监听折叠状态
const toggleCollapse = () => {
  emit('update:collapsed', !props.collapsed);
};

// 监听当前选中的菜单项
watch(() => activeKey.value, (newKey) => {
  if (newKey) {
    selectedKeys.value = [newKey];
  }
}, { immediate: true });

// 组件挂载时，设置选中项
watch(() => props.menuList, () => {
  if (activeKey.value) {
    selectedKeys.value = [activeKey.value];
  }
}, { immediate: true });

// 监听路由变化，同步更新菜单选中状态
watch(() => route.path, () => {
  console.log('路由变化:', route.path);

  const menuCode = navigateService.findMenuCodeByPath(route.path) || activeKey.value;
  if (menuCode) {
    // 同步侧边栏和tab的状态
    selectedKeys.value = [menuCode];
    tabsStore.setActiveTab(menuCode);
    // 确保父菜单展开
    ensureParentMenuExpanded(menuCode);
  }
}, { immediate: true });
const initMenu = () => {
  // 默认展开所有一级菜单
  const firstLevelMenus = props.menuList.filter(item => !item.parentId || item.parentId === '0');
  openKeys.value = firstLevelMenus.map(item => item.menuId || item.menuCode);
  menuStore.setExpandedKeys(openKeys.value);
}
const setOnEmit = () => {
  // 每次切换系统都默认初始化展开
  emitter.on('init-menu', (collapsed) => {
    initMenu();
  })
}
onMounted(() => {
  setOnEmit();
  initMenu();
})


</script>

<style lang="scss" scoped>
:deep(.ant-menu-submenu-selected) {
  color: inherit !important;
}


.sidebar-container {
  height: 100%;
  position: relative;

  .sidebar-footer {
    width: 100%;
    height: $sidebar-footer-height;
    background-color: $bg-color-sidebar-footer;
    position: absolute;
    bottom: 0;
    text-align: center;
    line-height: $sidebar-footer-height;
    z-index: 999;
    border: 1px solid rgba(0, 0, 0, .05);

    &:hover {
      cursor: pointer;
      background-color: $hover-color-sidebar-footer;
    }

    .icon-collapsed {
      @include base-icon(18px, 'icon-collapsed.svg');
    }
  }
}
</style>